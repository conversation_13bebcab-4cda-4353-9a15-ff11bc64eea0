/**
 * TerrainSystemNodes.ts
 * 
 * 地形系统节点 - 提供地形生成、雕刻、绘制等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 地形生成器节点
 */
export class TerrainGeneratorNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'terrain/generator/terrainGenerator',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('size', SocketType.VECTOR3, { x: 512, y: 100, z: 512 }, '地形尺寸');
    this.addInputSocket('resolution', SocketType.NUMBER, 256, '分辨率');
    this.addInputSocket('heightScale', SocketType.NUMBER, 100, '高度缩放');
    this.addInputSocket('noiseType', SocketType.STRING, 'perlin', '噪声类型');
    this.addInputSocket('seed', SocketType.NUMBER, 12345, '随机种子');
    this.addInputSocket('octaves', SocketType.NUMBER, 4, '噪声层数');
    this.addInputSocket('frequency', SocketType.NUMBER, 0.01, '频率');
    this.addInputSocket('amplitude', SocketType.NUMBER, 1.0, '振幅');
    
    // 输出插槽
    this.addOutputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addOutputSocket('heightMap', SocketType.OBJECT, null, '高度图');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '生成成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const size = this.getInputValue('size') as { x: number, y: number, z: number };
    const resolution = this.getInputValue('resolution') as number;
    const heightScale = this.getInputValue('heightScale') as number;
    const noiseType = this.getInputValue('noiseType') as string;
    const seed = this.getInputValue('seed') as number;
    const octaves = this.getInputValue('octaves') as number;
    const frequency = this.getInputValue('frequency') as number;
    const amplitude = this.getInputValue('amplitude') as number;

    try {
      const world = this.context.getWorld();
      const terrainSystem = world.getSystem('TerrainSystem');
      
      if (!terrainSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '地形系统不可用' };
      }

      // 生成地形
      const result = await terrainSystem.generateTerrain({
        size,
        resolution,
        heightScale,
        noiseType,
        seed,
        octaves,
        frequency,
        amplitude
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('terrain', result.terrain);
      this.setOutputValue('heightMap', result.heightMap);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 地形雕刻节点
 */
export class TerrainSculptNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'terrain/sculpt/terrainSculpt',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addInputSocket('position', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '雕刻位置');
    this.addInputSocket('brushSize', SocketType.NUMBER, 10, '笔刷大小');
    this.addInputSocket('brushStrength', SocketType.NUMBER, 0.5, '笔刷强度');
    this.addInputSocket('sculptType', SocketType.STRING, 'raise', '雕刻类型');
    this.addInputSocket('falloff', SocketType.STRING, 'smooth', '衰减类型');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '雕刻成功');
    this.addOutputSocket('modifiedArea', SocketType.OBJECT, null, '修改区域');
  }

  protected executeImpl(): any {
    const terrain = this.getInputValue('terrain') as Entity;
    const position = this.getInputValue('position') as { x: number, y: number, z: number };
    const brushSize = this.getInputValue('brushSize') as number;
    const brushStrength = this.getInputValue('brushStrength') as number;
    const sculptType = this.getInputValue('sculptType') as string;
    const falloff = this.getInputValue('falloff') as string;

    if (!terrain) {
      this.setOutputValue('success', false);
      return { success: false, error: '地形实体不能为空' };
    }

    try {
      // 获取地形组件
      const terrainComponent = terrain.getComponent('TerrainComponent');
      if (!terrainComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有地形组件' };
      }

      // 执行雕刻
      const result = terrainComponent.sculpt({
        position,
        brushSize,
        brushStrength,
        sculptType,
        falloff
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('modifiedArea', result.modifiedArea);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 地形绘制节点
 */
export class TerrainPaintNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'terrain/paint/terrainPaint',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addInputSocket('position', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '绘制位置');
    this.addInputSocket('brushSize', SocketType.NUMBER, 10, '笔刷大小');
    this.addInputSocket('brushStrength', SocketType.NUMBER, 0.5, '笔刷强度');
    this.addInputSocket('textureIndex', SocketType.NUMBER, 0, '纹理索引');
    this.addInputSocket('blendMode', SocketType.STRING, 'alpha', '混合模式');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '绘制成功');
    this.addOutputSocket('paintedArea', SocketType.OBJECT, null, '绘制区域');
  }

  protected executeImpl(): any {
    const terrain = this.getInputValue('terrain') as Entity;
    const position = this.getInputValue('position') as { x: number, y: number, z: number };
    const brushSize = this.getInputValue('brushSize') as number;
    const brushStrength = this.getInputValue('brushStrength') as number;
    const textureIndex = this.getInputValue('textureIndex') as number;
    const blendMode = this.getInputValue('blendMode') as string;

    if (!terrain) {
      this.setOutputValue('success', false);
      return { success: false, error: '地形实体不能为空' };
    }

    try {
      // 获取地形组件
      const terrainComponent = terrain.getComponent('TerrainComponent');
      if (!terrainComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有地形组件' };
      }

      // 执行纹理绘制
      const result = terrainComponent.paintTexture({
        position,
        brushSize,
        brushStrength,
        textureIndex,
        blendMode
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('paintedArea', result.paintedArea);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 地形碰撞节点
 */
export class TerrainCollisionNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'terrain/physics/terrainCollision',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addInputSocket('generateCollision', SocketType.BOOLEAN, true, '生成碰撞');
    this.addInputSocket('collisionLOD', SocketType.NUMBER, 1, '碰撞LOD');
    this.addInputSocket('physicsMaterial', SocketType.STRING, 'default', '物理材质');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('collisionMesh', SocketType.OBJECT, null, '碰撞网格');
  }

  protected executeImpl(): any {
    const terrain = this.getInputValue('terrain') as Entity;
    const generateCollision = this.getInputValue('generateCollision') as boolean;
    const collisionLOD = this.getInputValue('collisionLOD') as number;
    const physicsMaterial = this.getInputValue('physicsMaterial') as string;

    if (!terrain) {
      this.setOutputValue('success', false);
      return { success: false, error: '地形实体不能为空' };
    }

    try {
      // 获取地形组件
      const terrainComponent = terrain.getComponent('TerrainComponent');
      if (!terrainComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有地形组件' };
      }

      // 设置碰撞
      const result = terrainComponent.setupCollision({
        generateCollision,
        collisionLOD,
        physicsMaterial
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('collisionMesh', result.collisionMesh);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 地形LOD节点
 */
export class TerrainLODNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'terrain/optimization/terrainLOD',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addInputSocket('lodLevels', SocketType.NUMBER, 4, 'LOD级别数');
    this.addInputSocket('lodDistances', SocketType.ARRAY, [50, 100, 200, 400], 'LOD距离');
    this.addInputSocket('enableLOD', SocketType.BOOLEAN, true, '启用LOD');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('currentLOD', SocketType.NUMBER, 0, '当前LOD级别');
  }

  protected executeImpl(): any {
    const terrain = this.getInputValue('terrain') as Entity;
    const lodLevels = this.getInputValue('lodLevels') as number;
    const lodDistances = this.getInputValue('lodDistances') as number[];
    const enableLOD = this.getInputValue('enableLOD') as boolean;

    if (!terrain) {
      this.setOutputValue('success', false);
      return { success: false, error: '地形实体不能为空' };
    }

    try {
      // 获取地形组件
      const terrainComponent = terrain.getComponent('TerrainComponent');
      if (!terrainComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有地形组件' };
      }

      // 设置LOD
      const result = terrainComponent.setupLOD({
        lodLevels,
        lodDistances,
        enableLOD
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('currentLOD', result.currentLOD || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册地形系统节点
 */
export function registerTerrainSystemNodes(registry: NodeRegistry): void {
  // 地形生成节点
  registry.registerNodeType({
    type: 'terrain/generator/terrainGenerator',
    category: NodeCategory.CUSTOM,
    constructor: TerrainGeneratorNode,
    label: '地形生成器',
    description: '程序化生成地形',
    tags: ['terrain', 'generator', 'procedural'],
    version: '1.0.0'
  });

  // 地形编辑节点
  registry.registerNodeType({
    type: 'terrain/sculpt/terrainSculpt',
    category: NodeCategory.CUSTOM,
    constructor: TerrainSculptNode,
    label: '地形雕刻',
    description: '雕刻地形高度',
    tags: ['terrain', 'sculpt', 'edit'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'terrain/paint/terrainPaint',
    category: NodeCategory.CUSTOM,
    constructor: TerrainPaintNode,
    label: '地形绘制',
    description: '绘制地形纹理',
    tags: ['terrain', 'paint', 'texture'],
    version: '1.0.0'
  });

  // 地形物理节点
  registry.registerNodeType({
    type: 'terrain/physics/terrainCollision',
    category: NodeCategory.CUSTOM,
    constructor: TerrainCollisionNode,
    label: '地形碰撞',
    description: '设置地形碰撞',
    tags: ['terrain', 'physics', 'collision'],
    version: '1.0.0'
  });

  // 地形优化节点
  registry.registerNodeType({
    type: 'terrain/optimization/terrainLOD',
    category: NodeCategory.CUSTOM,
    constructor: TerrainLODNode,
    label: '地形LOD',
    description: '设置地形细节层次',
    tags: ['terrain', 'lod', 'optimization'],
    version: '1.0.0'
  });
}

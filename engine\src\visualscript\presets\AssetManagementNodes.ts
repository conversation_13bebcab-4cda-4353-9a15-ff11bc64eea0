/**
 * AssetManagementNodes.ts
 * 
 * 资产管理节点 - 提供资源加载、卸载、依赖管理等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 加载资产节点
 */
export class LoadAssetNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'asset/loadAsset',
      category: NodeCategory.FILE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('assetPath', SocketType.STRING, '', '资产路径');
    this.addInputSocket('assetType', SocketType.STRING, 'auto', '资产类型');
    this.addInputSocket('loadOptions', SocketType.OBJECT, {}, '加载选项');
    
    // 输出插槽
    this.addOutputSocket('asset', SocketType.ANY, null, '加载的资产');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '加载成功');
    this.addOutputSocket('assetId', SocketType.STRING, '', '资产ID');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const assetPath = this.getInputValue('assetPath') as string;
    const assetType = this.getInputValue('assetType') as string;
    const loadOptions = this.getInputValue('loadOptions') as any;

    if (!assetPath) {
      this.setOutputValue('success', false);
      return { success: false, error: '资产路径不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const assetManager = world.getSystem('AssetManager');
      
      if (!assetManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '资产管理器不可用' };
      }

      const result = await assetManager.loadAsset(assetPath, assetType, loadOptions);
      
      if (result.success) {
        this.setOutputValue('asset', result.asset);
        this.setOutputValue('success', true);
        this.setOutputValue('assetId', result.assetId);
      } else {
        this.setOutputValue('success', false);
      }
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 卸载资产节点
 */
export class UnloadAssetNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'asset/unloadAsset',
      category: NodeCategory.FILE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('assetId', SocketType.STRING, '', '资产ID');
    this.addInputSocket('force', SocketType.BOOLEAN, false, '强制卸载');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '卸载成功');
  }

  protected executeImpl(): any {
    const assetId = this.getInputValue('assetId') as string;
    const force = this.getInputValue('force') as boolean;

    if (!assetId) {
      this.setOutputValue('success', false);
      return { success: false, error: '资产ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const assetManager = world.getSystem('AssetManager');
      
      if (!assetManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '资产管理器不可用' };
      }

      const result = assetManager.unloadAsset(assetId, force);
      this.setOutputValue('success', result.success);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取资产依赖节点
 */
export class GetAssetDependenciesNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'asset/getAssetDependencies',
      category: NodeCategory.FILE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('assetId', SocketType.STRING, '', '资产ID');
    this.addInputSocket('recursive', SocketType.BOOLEAN, true, '递归获取');
    
    // 输出插槽
    this.addOutputSocket('dependencies', SocketType.ARRAY, [], '依赖列表');
    this.addOutputSocket('count', SocketType.NUMBER, 0, '依赖数量');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '获取成功');
  }

  protected executeImpl(): any {
    const assetId = this.getInputValue('assetId') as string;
    const recursive = this.getInputValue('recursive') as boolean;

    if (!assetId) {
      this.setOutputValue('success', false);
      return { success: false, error: '资产ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const assetManager = world.getSystem('AssetManager');
      
      if (!assetManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '资产管理器不可用' };
      }

      const dependencies = assetManager.getAssetDependencies(assetId, recursive);
      
      this.setOutputValue('dependencies', dependencies);
      this.setOutputValue('count', dependencies.length);
      this.setOutputValue('success', true);
      
      return { success: true, dependencies, count: dependencies.length };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 预加载资产节点
 */
export class PreloadAssetsNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'asset/preloadAssets',
      category: NodeCategory.FILE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('assetPaths', SocketType.ARRAY, [], '资产路径列表');
    this.addInputSocket('priority', SocketType.NUMBER, 0, '加载优先级');
    this.addInputSocket('maxConcurrent', SocketType.NUMBER, 4, '最大并发数');
    
    // 输出插槽
    this.addOutputSocket('loadedAssets', SocketType.ARRAY, [], '已加载资产');
    this.addOutputSocket('failedAssets', SocketType.ARRAY, [], '加载失败资产');
    this.addOutputSocket('progress', SocketType.NUMBER, 0, '加载进度');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '预加载成功');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const assetPaths = this.getInputValue('assetPaths') as string[];
    const priority = this.getInputValue('priority') as number;
    const maxConcurrent = this.getInputValue('maxConcurrent') as number;

    if (!assetPaths || assetPaths.length === 0) {
      this.setOutputValue('success', false);
      return { success: false, error: '资产路径列表不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const assetManager = world.getSystem('AssetManager');
      
      if (!assetManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '资产管理器不可用' };
      }

      const loadedAssets: any[] = [];
      const failedAssets: string[] = [];
      let completed = 0;

      // 分批加载资产
      for (let i = 0; i < assetPaths.length; i += maxConcurrent) {
        const batch = assetPaths.slice(i, i + maxConcurrent);
        const promises = batch.map(async (path) => {
          try {
            const result = await assetManager.loadAsset(path, 'auto', { priority });
            if (result.success) {
              loadedAssets.push(result.asset);
            } else {
              failedAssets.push(path);
            }
          } catch (error) {
            failedAssets.push(path);
          }
          completed++;
          
          // 更新进度
          const progress = completed / assetPaths.length;
          this.setOutputValue('progress', progress);
        });

        await Promise.all(promises);
      }

      this.setOutputValue('loadedAssets', loadedAssets);
      this.setOutputValue('failedAssets', failedAssets);
      this.setOutputValue('progress', 1.0);
      this.setOutputValue('success', failedAssets.length === 0);
      
      return { 
        success: failedAssets.length === 0, 
        loadedAssets, 
        failedAssets,
        progress: 1.0
      };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取资产信息节点
 */
export class GetAssetInfoNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'asset/getAssetInfo',
      category: NodeCategory.FILE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('assetId', SocketType.STRING, '', '资产ID');
    
    // 输出插槽
    this.addOutputSocket('assetInfo', SocketType.OBJECT, {}, '资产信息');
    this.addOutputSocket('isLoaded', SocketType.BOOLEAN, false, '是否已加载');
    this.addOutputSocket('loadTime', SocketType.NUMBER, 0, '加载时间');
    this.addOutputSocket('memoryUsage', SocketType.NUMBER, 0, '内存使用');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '获取成功');
  }

  protected executeImpl(): any {
    const assetId = this.getInputValue('assetId') as string;

    if (!assetId) {
      this.setOutputValue('success', false);
      return { success: false, error: '资产ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const assetManager = world.getSystem('AssetManager');
      
      if (!assetManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '资产管理器不可用' };
      }

      const assetInfo = assetManager.getAssetInfo(assetId);
      
      if (assetInfo) {
        this.setOutputValue('assetInfo', assetInfo);
        this.setOutputValue('isLoaded', assetInfo.isLoaded || false);
        this.setOutputValue('loadTime', assetInfo.loadTime || 0);
        this.setOutputValue('memoryUsage', assetInfo.memoryUsage || 0);
        this.setOutputValue('success', true);
        
        return { success: true, assetInfo };
      } else {
        this.setOutputValue('success', false);
        return { success: false, error: '找不到资产信息' };
      }
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册资产管理节点
 */
export function registerAssetManagementNodes(registry: NodeRegistry): void {
  // 资产加载节点
  registry.registerNodeType({
    type: 'asset/loadAsset',
    category: NodeCategory.FILE,
    constructor: LoadAssetNode,
    label: '加载资产',
    description: '异步加载资产文件',
    tags: ['asset', 'load'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'asset/unloadAsset',
    category: NodeCategory.FILE,
    constructor: UnloadAssetNode,
    label: '卸载资产',
    description: '卸载资产并释放内存',
    tags: ['asset', 'unload'],
    version: '1.0.0'
  });

  // 资产依赖管理节点
  registry.registerNodeType({
    type: 'asset/getAssetDependencies',
    category: NodeCategory.FILE,
    constructor: GetAssetDependenciesNode,
    label: '获取资产依赖',
    description: '获取资产的依赖关系',
    tags: ['asset', 'dependency'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'asset/preloadAssets',
    category: NodeCategory.FILE,
    constructor: PreloadAssetsNode,
    label: '预加载资产',
    description: '批量预加载资产',
    tags: ['asset', 'preload'],
    version: '1.0.0'
  });

  // 资产信息节点
  registry.registerNodeType({
    type: 'asset/getAssetInfo',
    category: NodeCategory.FILE,
    constructor: GetAssetInfoNode,
    label: '获取资产信息',
    description: '获取资产的详细信息',
    tags: ['asset', 'info'],
    version: '1.0.0'
  });
}

/**
 * AdvancedAnimationNodes.ts
 * 
 * 高级动画节点 - 提供IK、动画重定向、混合形状等高级动画功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * IK求解器节点
 */
export class IKSolverNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'animation/ik/ikSolver',
      category: NodeCategory.ANIMATION
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('targetPosition', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '目标位置');
    this.addInputSocket('chainLength', SocketType.NUMBER, 3, '骨骼链长度');
    this.addInputSocket('iterations', SocketType.NUMBER, 10, '迭代次数');
    this.addInputSocket('tolerance', SocketType.NUMBER, 0.01, '容差');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '求解成功');
    this.addOutputSocket('finalPosition', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '最终位置');
    this.addOutputSocket('error', SocketType.NUMBER, 0, '误差值');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const targetPosition = this.getInputValue('targetPosition') as { x: number, y: number, z: number };
    const chainLength = this.getInputValue('chainLength') as number;
    const iterations = this.getInputValue('iterations') as number;
    const tolerance = this.getInputValue('tolerance') as number;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取IK系统
      const ikSystem = animationComponent.getIKSystem();
      if (!ikSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'IK系统不可用' };
      }

      // 执行IK求解
      const result = ikSystem.solve({
        targetPosition,
        chainLength,
        iterations,
        tolerance
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('finalPosition', result.finalPosition || { x: 0, y: 0, z: 0 });
      this.setOutputValue('error', result.error || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * IK目标节点
 */
export class IKTargetNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'animation/ik/ikTarget',
      category: NodeCategory.ANIMATION
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('targetEntity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('boneName', SocketType.STRING, '', '骨骼名称');
    this.addInputSocket('weight', SocketType.NUMBER, 1.0, '权重');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('ikTarget', SocketType.OBJECT, null, 'IK目标对象');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const targetEntity = this.getInputValue('targetEntity') as Entity;
    const boneName = this.getInputValue('boneName') as string;
    const weight = this.getInputValue('weight') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    if (!targetEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '目标实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取IK系统
      const ikSystem = animationComponent.getIKSystem();
      if (!ikSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'IK系统不可用' };
      }

      // 创建IK目标
      const ikTarget = ikSystem.createTarget({
        targetEntity,
        boneName,
        weight,
        enabled
      });

      this.setOutputValue('success', true);
      this.setOutputValue('ikTarget', ikTarget);
      
      return { success: true, ikTarget };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 动画重定向节点
 */
export class RetargetAnimationNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'animation/retarget/retargetAnimation',
      category: NodeCategory.ANIMATION
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('sourceEntity', SocketType.ENTITY, null, '源实体');
    this.addInputSocket('targetEntity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('animationClip', SocketType.STRING, '', '动画片段');
    this.addInputSocket('boneMapping', SocketType.OBJECT, {}, '骨骼映射');
    this.addInputSocket('scaleMode', SocketType.STRING, 'proportional', '缩放模式');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '重定向成功');
    this.addOutputSocket('retargetedClip', SocketType.OBJECT, null, '重定向后的动画片段');
  }

  protected executeImpl(): any {
    const sourceEntity = this.getInputValue('sourceEntity') as Entity;
    const targetEntity = this.getInputValue('targetEntity') as Entity;
    const animationClip = this.getInputValue('animationClip') as string;
    const boneMapping = this.getInputValue('boneMapping') as any;
    const scaleMode = this.getInputValue('scaleMode') as string;

    if (!sourceEntity || !targetEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '源实体和目标实体不能为空' };
    }

    try {
      // 获取动画组件
      const sourceAnimation = sourceEntity.getComponent('AnimationComponent');
      const targetAnimation = targetEntity.getComponent('AnimationComponent');
      
      if (!sourceAnimation || !targetAnimation) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体缺少动画组件' };
      }

      // 获取重定向系统
      const retargetSystem = sourceAnimation.getRetargetSystem();
      if (!retargetSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '重定向系统不可用' };
      }

      // 执行动画重定向
      const result = retargetSystem.retargetAnimation({
        sourceEntity,
        targetEntity,
        animationClip,
        boneMapping,
        scaleMode
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('retargetedClip', result.retargetedClip);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 混合形状控制节点
 */
export class BlendShapeControlNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'animation/blendshape/blendShapeControl',
      category: NodeCategory.ANIMATION
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('blendShapeName', SocketType.STRING, '', '混合形状名称');
    this.addInputSocket('weight', SocketType.NUMBER, 0.0, '权重值');
    this.addInputSocket('duration', SocketType.NUMBER, 0.0, '过渡时间');
    this.addInputSocket('easeType', SocketType.STRING, 'linear', '缓动类型');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('currentWeight', SocketType.NUMBER, 0.0, '当前权重');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const blendShapeName = this.getInputValue('blendShapeName') as string;
    const weight = this.getInputValue('weight') as number;
    const duration = this.getInputValue('duration') as number;
    const easeType = this.getInputValue('easeType') as string;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    if (!blendShapeName) {
      this.setOutputValue('success', false);
      return { success: false, error: '混合形状名称不能为空' };
    }

    try {
      // 获取渲染组件
      const renderComponent = entity.getComponent('RenderComponent');
      if (!renderComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有渲染组件' };
      }

      // 获取混合形状系统
      const blendShapeSystem = renderComponent.getBlendShapeSystem();
      if (!blendShapeSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '混合形状系统不可用' };
      }

      // 设置混合形状权重
      const result = blendShapeSystem.setBlendShapeWeight({
        name: blendShapeName,
        weight,
        duration,
        easeType
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('currentWeight', result.currentWeight || 0.0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 动画层混合节点
 */
export class AnimationLayerBlendNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'animation/blend/animationLayerBlend',
      category: NodeCategory.ANIMATION
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('layerName', SocketType.STRING, '', '动画层名称');
    this.addInputSocket('blendMode', SocketType.STRING, 'additive', '混合模式');
    this.addInputSocket('weight', SocketType.NUMBER, 1.0, '层权重');
    this.addInputSocket('maskBones', SocketType.ARRAY, [], '遮罩骨骼');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('layerId', SocketType.STRING, '', '动画层ID');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const layerName = this.getInputValue('layerName') as string;
    const blendMode = this.getInputValue('blendMode') as string;
    const weight = this.getInputValue('weight') as number;
    const maskBones = this.getInputValue('maskBones') as string[];

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 创建或更新动画层
      const result = animationComponent.createAnimationLayer({
        name: layerName,
        blendMode,
        weight,
        maskBones
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('layerId', result.layerId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册高级动画节点
 */
export function registerAdvancedAnimationNodes(registry: NodeRegistry): void {
  // IK系统节点
  registry.registerNodeType({
    type: 'animation/ik/ikSolver',
    category: NodeCategory.ANIMATION,
    constructor: IKSolverNode,
    label: 'IK求解器',
    description: '反向动力学求解器',
    tags: ['animation', 'ik', 'solver'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'animation/ik/ikTarget',
    category: NodeCategory.ANIMATION,
    constructor: IKTargetNode,
    label: 'IK目标',
    description: '设置IK目标',
    tags: ['animation', 'ik', 'target'],
    version: '1.0.0'
  });

  // 动画重定向节点
  registry.registerNodeType({
    type: 'animation/retarget/retargetAnimation',
    category: NodeCategory.ANIMATION,
    constructor: RetargetAnimationNode,
    label: '动画重定向',
    description: '将动画从一个骨架重定向到另一个骨架',
    tags: ['animation', 'retarget'],
    version: '1.0.0'
  });

  // 混合形状节点
  registry.registerNodeType({
    type: 'animation/blendshape/blendShapeControl',
    category: NodeCategory.ANIMATION,
    constructor: BlendShapeControlNode,
    label: '混合形状控制',
    description: '控制混合形状权重',
    tags: ['animation', 'blendshape'],
    version: '1.0.0'
  });

  // 动画层混合节点
  registry.registerNodeType({
    type: 'animation/blend/animationLayerBlend',
    category: NodeCategory.ANIMATION,
    constructor: AnimationLayerBlendNode,
    label: '动画层混合',
    description: '创建和管理动画层',
    tags: ['animation', 'layer', 'blend'],
    version: '1.0.0'
  });
}

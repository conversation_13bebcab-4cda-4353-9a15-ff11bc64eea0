/**
 * NodeCoverageTest.ts
 * 
 * 视觉脚本节点覆盖率测试 - 验证所有节点类型的注册和功能
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { registerOptimizedNodes } from '../presets/OptimizedNodeRegistry';

/**
 * 节点覆盖率测试类
 */
export class NodeCoverageTest {
  private registry: NodeRegistry;
  private testResults: Map<string, TestResult> = new Map();

  constructor() {
    this.registry = new NodeRegistry();
    this.initializeRegistry();
  }

  /**
   * 初始化节点注册表
   */
  private initializeRegistry(): void {
    try {
      registerOptimizedNodes(this.registry);
      console.log('✅ 节点注册表初始化成功');
    } catch (error) {
      console.error('❌ 节点注册表初始化失败:', error);
      throw error;
    }
  }

  /**
   * 运行完整的覆盖率测试
   */
  public async runFullCoverageTest(): Promise<CoverageReport> {
    console.log('🚀 开始运行节点覆盖率测试...\n');

    const report: CoverageReport = {
      totalNodes: 0,
      testedNodes: 0,
      passedNodes: 0,
      failedNodes: 0,
      coverage: 0,
      categoryResults: new Map(),
      detailedResults: new Map(),
      timestamp: new Date()
    };

    // 获取所有注册的节点类型
    const allNodeTypes = this.registry.getAllNodeTypes();
    report.totalNodes = allNodeTypes.length;

    console.log(`📊 发现 ${allNodeTypes.length} 个节点类型\n`);

    // 按类别分组测试
    const nodesByCategory = this.groupNodesByCategory(allNodeTypes);

    for (const [category, nodes] of nodesByCategory) {
      console.log(`🔍 测试类别: ${category} (${nodes.length} 个节点)`);
      
      const categoryResult: CategoryResult = {
        category,
        totalNodes: nodes.length,
        testedNodes: 0,
        passedNodes: 0,
        failedNodes: 0,
        coverage: 0,
        nodeResults: []
      };

      for (const nodeType of nodes) {
        const testResult = await this.testNodeType(nodeType);
        categoryResult.nodeResults.push(testResult);
        categoryResult.testedNodes++;
        
        if (testResult.passed) {
          categoryResult.passedNodes++;
          report.passedNodes++;
        } else {
          categoryResult.failedNodes++;
          report.failedNodes++;
        }

        report.testedNodes++;
        this.testResults.set(nodeType.type, testResult);
      }

      categoryResult.coverage = (categoryResult.passedNodes / categoryResult.totalNodes) * 100;
      report.categoryResults.set(category, categoryResult);
      
      console.log(`   ✅ 通过: ${categoryResult.passedNodes}`);
      console.log(`   ❌ 失败: ${categoryResult.failedNodes}`);
      console.log(`   📈 覆盖率: ${categoryResult.coverage.toFixed(1)}%\n`);
    }

    report.coverage = (report.passedNodes / report.totalNodes) * 100;
    report.detailedResults = this.testResults;

    this.printFinalReport(report);
    return report;
  }

  /**
   * 按类别分组节点
   */
  private groupNodesByCategory(nodeTypes: any[]): Map<string, any[]> {
    const grouped = new Map<string, any[]>();
    
    for (const nodeType of nodeTypes) {
      const category = nodeType.category || NodeCategory.CUSTOM;
      if (!grouped.has(category)) {
        grouped.set(category, []);
      }
      grouped.get(category)!.push(nodeType);
    }
    
    return grouped;
  }

  /**
   * 测试单个节点类型
   */
  private async testNodeType(nodeType: any): Promise<TestResult> {
    const result: TestResult = {
      nodeType: nodeType.type,
      label: nodeType.label,
      category: nodeType.category,
      passed: false,
      errors: [],
      warnings: [],
      testDuration: 0
    };

    const startTime = Date.now();

    try {
      // 1. 测试节点构造函数
      await this.testNodeConstruction(nodeType, result);
      
      // 2. 测试节点插槽初始化
      await this.testSocketInitialization(nodeType, result);
      
      // 3. 测试节点执行（如果可能）
      await this.testNodeExecution(nodeType, result);
      
      // 4. 测试节点序列化
      await this.testNodeSerialization(nodeType, result);

      result.passed = result.errors.length === 0;
      
    } catch (error) {
      result.errors.push(`测试异常: ${error.message}`);
      result.passed = false;
    }

    result.testDuration = Date.now() - startTime;
    return result;
  }

  /**
   * 测试节点构造
   */
  private async testNodeConstruction(nodeType: any, result: TestResult): Promise<void> {
    try {
      const NodeClass = nodeType.constructor;
      if (!NodeClass) {
        result.errors.push('节点构造函数未定义');
        return;
      }

      const node = new NodeClass({
        id: `test_${nodeType.type}_${Date.now()}`,
        type: nodeType.type
      });

      if (!node) {
        result.errors.push('节点构造失败');
        return;
      }

      // 验证基本属性
      if (!node.type || node.type !== nodeType.type) {
        result.errors.push('节点类型不匹配');
      }

      if (!node.id) {
        result.errors.push('节点ID未设置');
      }

    } catch (error) {
      result.errors.push(`节点构造异常: ${error.message}`);
    }
  }

  /**
   * 测试插槽初始化
   */
  private async testSocketInitialization(nodeType: any, result: TestResult): Promise<void> {
    try {
      const NodeClass = nodeType.constructor;
      const node = new NodeClass({
        id: `test_socket_${nodeType.type}_${Date.now()}`,
        type: nodeType.type
      });

      // 检查输入插槽
      const inputSockets = node.getInputSockets ? node.getInputSockets() : [];
      const outputSockets = node.getOutputSockets ? node.getOutputSockets() : [];

      if (inputSockets.length === 0 && outputSockets.length === 0) {
        result.warnings.push('节点没有定义任何插槽');
      }

      // 验证插槽结构
      for (const socket of [...inputSockets, ...outputSockets]) {
        if (!socket.name || !socket.type) {
          result.errors.push(`插槽缺少必要属性: ${socket.name || '未命名'}`);
        }
      }

    } catch (error) {
      result.errors.push(`插槽初始化异常: ${error.message}`);
    }
  }

  /**
   * 测试节点执行
   */
  private async testNodeExecution(nodeType: any, result: TestResult): Promise<void> {
    try {
      const NodeClass = nodeType.constructor;
      const node = new NodeClass({
        id: `test_exec_${nodeType.type}_${Date.now()}`,
        type: nodeType.type
      });

      // 模拟执行上下文
      const mockContext = {
        getWorld: () => ({
          getSystem: (name: string) => null,
          createEntity: (name: string) => null,
          getRootEntity: () => null
        }),
        getVariable: (name: string) => null,
        setVariable: (name: string, value: any) => {},
        log: (message: string) => {}
      };

      if (node.setContext) {
        node.setContext(mockContext);
      }

      // 尝试执行节点（如果有execute方法）
      if (node.execute) {
        try {
          await node.execute();
        } catch (error) {
          // 执行失败是预期的，因为我们使用的是模拟上下文
          result.warnings.push(`节点执行需要真实环境: ${error.message}`);
        }
      }

    } catch (error) {
      result.errors.push(`节点执行测试异常: ${error.message}`);
    }
  }

  /**
   * 测试节点序列化
   */
  private async testNodeSerialization(nodeType: any, result: TestResult): Promise<void> {
    try {
      const NodeClass = nodeType.constructor;
      const node = new NodeClass({
        id: `test_serial_${nodeType.type}_${Date.now()}`,
        type: nodeType.type
      });

      // 测试序列化
      if (node.serialize) {
        const serialized = node.serialize();
        if (!serialized || typeof serialized !== 'object') {
          result.errors.push('节点序列化失败');
        }
      }

      // 测试反序列化
      if (node.deserialize && node.serialize) {
        const serialized = node.serialize();
        try {
          node.deserialize(serialized);
        } catch (error) {
          result.errors.push(`节点反序列化失败: ${error.message}`);
        }
      }

    } catch (error) {
      result.errors.push(`节点序列化测试异常: ${error.message}`);
    }
  }

  /**
   * 打印最终报告
   */
  private printFinalReport(report: CoverageReport): void {
    console.log('📋 ===== 节点覆盖率测试报告 =====');
    console.log(`🕒 测试时间: ${report.timestamp.toLocaleString()}`);
    console.log(`📊 总节点数: ${report.totalNodes}`);
    console.log(`🧪 已测试: ${report.testedNodes}`);
    console.log(`✅ 通过: ${report.passedNodes}`);
    console.log(`❌ 失败: ${report.failedNodes}`);
    console.log(`📈 总覆盖率: ${report.coverage.toFixed(1)}%`);
    console.log('');

    // 按类别显示结果
    console.log('📂 分类别结果:');
    for (const [category, categoryResult] of report.categoryResults) {
      console.log(`  ${category}: ${categoryResult.coverage.toFixed(1)}% (${categoryResult.passedNodes}/${categoryResult.totalNodes})`);
    }
    console.log('');

    // 显示失败的节点
    const failedNodes = Array.from(report.detailedResults.values()).filter(r => !r.passed);
    if (failedNodes.length > 0) {
      console.log('❌ 失败的节点:');
      for (const failed of failedNodes) {
        console.log(`  - ${failed.nodeType}: ${failed.errors.join(', ')}`);
      }
    }

    console.log('================================\n');
  }
}

// 类型定义
interface TestResult {
  nodeType: string;
  label: string;
  category: string;
  passed: boolean;
  errors: string[];
  warnings: string[];
  testDuration: number;
}

interface CategoryResult {
  category: string;
  totalNodes: number;
  testedNodes: number;
  passedNodes: number;
  failedNodes: number;
  coverage: number;
  nodeResults: TestResult[];
}

interface CoverageReport {
  totalNodes: number;
  testedNodes: number;
  passedNodes: number;
  failedNodes: number;
  coverage: number;
  categoryResults: Map<string, CategoryResult>;
  detailedResults: Map<string, TestResult>;
  timestamp: Date;
}

// 导出测试函数
export async function runNodeCoverageTest(): Promise<CoverageReport> {
  const tester = new NodeCoverageTest();
  return await tester.runFullCoverageTest();
}

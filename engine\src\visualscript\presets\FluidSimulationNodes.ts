/**
 * FluidSimulationNodes.ts
 * 
 * 流体模拟节点 - 提供流体模拟、粒子流体、流体渲染等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 流体模拟器节点
 */
export class FluidSimulatorNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'fluid/simulation/fluidSimulator',
      category: NodeCategory.PHYSICS
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('simulationType', SocketType.STRING, 'sph', '模拟类型');
    this.addInputSocket('particleCount', SocketType.NUMBER, 1000, '粒子数量');
    this.addInputSocket('viscosity', SocketType.NUMBER, 0.1, '粘度');
    this.addInputSocket('density', SocketType.NUMBER, 1000, '密度');
    this.addInputSocket('pressure', SocketType.NUMBER, 1.0, '压力');
    this.addInputSocket('gravity', SocketType.VECTOR3, { x: 0, y: -9.81, z: 0 }, '重力');
    this.addInputSocket('bounds', SocketType.VECTOR3, { x: 10, y: 10, z: 10 }, '边界');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '创建成功');
    this.addOutputSocket('simulator', SocketType.OBJECT, null, '模拟器对象');
    this.addOutputSocket('simulatorId', SocketType.STRING, '', '模拟器ID');
  }

  protected executeImpl(): any {
    const simulationType = this.getInputValue('simulationType') as string;
    const particleCount = this.getInputValue('particleCount') as number;
    const viscosity = this.getInputValue('viscosity') as number;
    const density = this.getInputValue('density') as number;
    const pressure = this.getInputValue('pressure') as number;
    const gravity = this.getInputValue('gravity') as { x: number, y: number, z: number };
    const bounds = this.getInputValue('bounds') as { x: number, y: number, z: number };

    try {
      const world = this.context.getWorld();
      const physicsSystem = world.getSystem('PhysicsSystem');
      
      if (!physicsSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '物理系统不可用' };
      }

      // 获取流体模拟系统
      const fluidSystem = physicsSystem.getFluidSystem();
      if (!fluidSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '流体模拟系统不可用' };
      }

      // 创建流体模拟器
      const result = fluidSystem.createSimulator({
        type: simulationType,
        particleCount,
        viscosity,
        density,
        pressure,
        gravity,
        bounds
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('simulator', result.simulator);
      this.setOutputValue('simulatorId', result.simulatorId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 粒子流体节点
 */
export class ParticleFluidNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'fluid/particle/particleFluid',
      category: NodeCategory.PHYSICS
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('simulator', SocketType.OBJECT, null, '流体模拟器');
    this.addInputSocket('emitterPosition', SocketType.VECTOR3, { x: 0, y: 5, z: 0 }, '发射器位置');
    this.addInputSocket('emissionRate', SocketType.NUMBER, 100, '发射速率');
    this.addInputSocket('particleLifetime', SocketType.NUMBER, 5.0, '粒子生命周期');
    this.addInputSocket('initialVelocity', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '初始速度');
    this.addInputSocket('particleSize', SocketType.NUMBER, 0.1, '粒子大小');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('emitterId', SocketType.STRING, '', '发射器ID');
    this.addOutputSocket('activeParticles', SocketType.NUMBER, 0, '活跃粒子数');
  }

  protected executeImpl(): any {
    const simulator = this.getInputValue('simulator') as any;
    const emitterPosition = this.getInputValue('emitterPosition') as { x: number, y: number, z: number };
    const emissionRate = this.getInputValue('emissionRate') as number;
    const particleLifetime = this.getInputValue('particleLifetime') as number;
    const initialVelocity = this.getInputValue('initialVelocity') as { x: number, y: number, z: number };
    const particleSize = this.getInputValue('particleSize') as number;

    if (!simulator) {
      this.setOutputValue('success', false);
      return { success: false, error: '流体模拟器不能为空' };
    }

    try {
      // 创建粒子发射器
      const result = simulator.createEmitter({
        position: emitterPosition,
        emissionRate,
        particleLifetime,
        initialVelocity,
        particleSize
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('emitterId', result.emitterId || '');
      this.setOutputValue('activeParticles', result.activeParticles || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 流体渲染器节点
 */
export class FluidRendererNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'fluid/rendering/fluidRenderer',
      category: NodeCategory.PHYSICS
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('simulator', SocketType.OBJECT, null, '流体模拟器');
    this.addInputSocket('renderMode', SocketType.STRING, 'surface', '渲染模式');
    this.addInputSocket('material', SocketType.STRING, 'water', '材质类型');
    this.addInputSocket('transparency', SocketType.NUMBER, 0.8, '透明度');
    this.addInputSocket('refractionIndex', SocketType.NUMBER, 1.33, '折射率');
    this.addInputSocket('enableReflection', SocketType.BOOLEAN, true, '启用反射');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('renderer', SocketType.OBJECT, null, '渲染器对象');
    this.addOutputSocket('rendererId', SocketType.STRING, '', '渲染器ID');
  }

  protected executeImpl(): any {
    const simulator = this.getInputValue('simulator') as any;
    const renderMode = this.getInputValue('renderMode') as string;
    const material = this.getInputValue('material') as string;
    const transparency = this.getInputValue('transparency') as number;
    const refractionIndex = this.getInputValue('refractionIndex') as number;
    const enableReflection = this.getInputValue('enableReflection') as boolean;

    if (!simulator) {
      this.setOutputValue('success', false);
      return { success: false, error: '流体模拟器不能为空' };
    }

    try {
      // 创建流体渲染器
      const result = simulator.createRenderer({
        renderMode,
        material,
        transparency,
        refractionIndex,
        enableReflection
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('renderer', result.renderer);
      this.setOutputValue('rendererId', result.rendererId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 流体碰撞节点
 */
export class FluidCollisionNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'fluid/physics/fluidCollision',
      category: NodeCategory.PHYSICS
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('simulator', SocketType.OBJECT, null, '流体模拟器');
    this.addInputSocket('collider', SocketType.ENTITY, null, '碰撞体');
    this.addInputSocket('bounciness', SocketType.NUMBER, 0.3, '弹性');
    this.addInputSocket('friction', SocketType.NUMBER, 0.5, '摩擦力');
    this.addInputSocket('adhesion', SocketType.NUMBER, 0.1, '粘附力');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('colliderId', SocketType.STRING, '', '碰撞体ID');
  }

  protected executeImpl(): any {
    const simulator = this.getInputValue('simulator') as any;
    const collider = this.getInputValue('collider') as Entity;
    const bounciness = this.getInputValue('bounciness') as number;
    const friction = this.getInputValue('friction') as number;
    const adhesion = this.getInputValue('adhesion') as number;

    if (!simulator) {
      this.setOutputValue('success', false);
      return { success: false, error: '流体模拟器不能为空' };
    }

    if (!collider) {
      this.setOutputValue('success', false);
      return { success: false, error: '碰撞体不能为空' };
    }

    try {
      // 添加流体碰撞体
      const result = simulator.addCollider({
        collider,
        bounciness,
        friction,
        adhesion
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('colliderId', result.colliderId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 流体力场节点
 */
export class FluidForceFieldNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'fluid/forces/fluidForceField',
      category: NodeCategory.PHYSICS
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('simulator', SocketType.OBJECT, null, '流体模拟器');
    this.addInputSocket('forceType', SocketType.STRING, 'directional', '力场类型');
    this.addInputSocket('position', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '力场位置');
    this.addInputSocket('direction', SocketType.VECTOR3, { x: 0, y: 1, z: 0 }, '力场方向');
    this.addInputSocket('strength', SocketType.NUMBER, 10.0, '力场强度');
    this.addInputSocket('radius', SocketType.NUMBER, 5.0, '影响半径');
    this.addInputSocket('falloff', SocketType.STRING, 'linear', '衰减类型');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('forceFieldId', SocketType.STRING, '', '力场ID');
  }

  protected executeImpl(): any {
    const simulator = this.getInputValue('simulator') as any;
    const forceType = this.getInputValue('forceType') as string;
    const position = this.getInputValue('position') as { x: number, y: number, z: number };
    const direction = this.getInputValue('direction') as { x: number, y: number, z: number };
    const strength = this.getInputValue('strength') as number;
    const radius = this.getInputValue('radius') as number;
    const falloff = this.getInputValue('falloff') as string;

    if (!simulator) {
      this.setOutputValue('success', false);
      return { success: false, error: '流体模拟器不能为空' };
    }

    try {
      // 创建力场
      const result = simulator.createForceField({
        type: forceType,
        position,
        direction,
        strength,
        radius,
        falloff
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('forceFieldId', result.forceFieldId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册流体模拟节点
 */
export function registerFluidSimulationNodes(registry: NodeRegistry): void {
  // 流体模拟器节点
  registry.registerNodeType({
    type: 'fluid/simulation/fluidSimulator',
    category: NodeCategory.PHYSICS,
    constructor: FluidSimulatorNode,
    label: '流体模拟器',
    description: '创建流体模拟器',
    tags: ['fluid', 'simulation', 'physics'],
    version: '1.0.0'
  });

  // 粒子流体节点
  registry.registerNodeType({
    type: 'fluid/particle/particleFluid',
    category: NodeCategory.PHYSICS,
    constructor: ParticleFluidNode,
    label: '粒子流体',
    description: '创建粒子流体发射器',
    tags: ['fluid', 'particle', 'emitter'],
    version: '1.0.0'
  });

  // 流体渲染节点
  registry.registerNodeType({
    type: 'fluid/rendering/fluidRenderer',
    category: NodeCategory.PHYSICS,
    constructor: FluidRendererNode,
    label: '流体渲染器',
    description: '设置流体渲染效果',
    tags: ['fluid', 'rendering', 'visual'],
    version: '1.0.0'
  });

  // 流体物理节点
  registry.registerNodeType({
    type: 'fluid/physics/fluidCollision',
    category: NodeCategory.PHYSICS,
    constructor: FluidCollisionNode,
    label: '流体碰撞',
    description: '设置流体碰撞体',
    tags: ['fluid', 'collision', 'physics'],
    version: '1.0.0'
  });

  // 流体力场节点
  registry.registerNodeType({
    type: 'fluid/forces/fluidForceField',
    category: NodeCategory.PHYSICS,
    constructor: FluidForceFieldNode,
    label: '流体力场',
    description: '创建影响流体的力场',
    tags: ['fluid', 'force', 'field'],
    version: '1.0.0'
  });
}

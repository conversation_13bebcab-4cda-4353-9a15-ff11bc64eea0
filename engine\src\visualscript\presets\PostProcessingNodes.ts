/**
 * PostProcessingNodes.ts
 * 
 * 后处理节点 - 提供后处理效果、滤镜、色彩调整等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 后处理效果节点
 */
export class PostProcessEffectNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/effect/postProcessEffect',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('effectType', SocketType.STRING, 'bloom', '效果类型');
    this.addInputSocket('intensity', SocketType.NUMBER, 1.0, '强度');
    this.addInputSocket('threshold', SocketType.NUMBER, 0.5, '阈值');
    this.addInputSocket('radius', SocketType.NUMBER, 1.0, '半径');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('effectId', SocketType.STRING, '', '效果ID');
  }

  protected executeImpl(): any {
    const effectType = this.getInputValue('effectType') as string;
    const intensity = this.getInputValue('intensity') as number;
    const threshold = this.getInputValue('threshold') as number;
    const radius = this.getInputValue('radius') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');
      
      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 创建或更新后处理效果
      const result = postProcessManager.addEffect({
        type: effectType,
        intensity,
        threshold,
        radius,
        enabled
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('effectId', result.effectId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 色彩调整节点
 */
export class ColorAdjustmentNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/color/colorAdjustment',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('brightness', SocketType.NUMBER, 0.0, '亮度');
    this.addInputSocket('contrast', SocketType.NUMBER, 1.0, '对比度');
    this.addInputSocket('saturation', SocketType.NUMBER, 1.0, '饱和度');
    this.addInputSocket('hue', SocketType.NUMBER, 0.0, '色相');
    this.addInputSocket('gamma', SocketType.NUMBER, 1.0, 'Gamma');
    this.addInputSocket('exposure', SocketType.NUMBER, 0.0, '曝光');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('adjustmentId', SocketType.STRING, '', '调整ID');
  }

  protected executeImpl(): any {
    const brightness = this.getInputValue('brightness') as number;
    const contrast = this.getInputValue('contrast') as number;
    const saturation = this.getInputValue('saturation') as number;
    const hue = this.getInputValue('hue') as number;
    const gamma = this.getInputValue('gamma') as number;
    const exposure = this.getInputValue('exposure') as number;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');
      
      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 设置色彩调整
      const result = postProcessManager.setColorAdjustment({
        brightness,
        contrast,
        saturation,
        hue,
        gamma,
        exposure
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('adjustmentId', result.adjustmentId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 抗锯齿节点
 */
export class AntiAliasingNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/aa/antiAliasing',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('aaType', SocketType.STRING, 'fxaa', '抗锯齿类型');
    this.addInputSocket('quality', SocketType.STRING, 'high', '质量');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('aaId', SocketType.STRING, '', '抗锯齿ID');
  }

  protected executeImpl(): any {
    const aaType = this.getInputValue('aaType') as string;
    const quality = this.getInputValue('quality') as string;
    const enabled = this.getInputValue('enabled') as boolean;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');
      
      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 设置抗锯齿
      const result = postProcessManager.setAntiAliasing({
        type: aaType,
        quality,
        enabled
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('aaId', result.aaId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 景深效果节点
 */
export class DepthOfFieldNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/dof/depthOfField',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('focusDistance', SocketType.NUMBER, 10.0, '焦距');
    this.addInputSocket('focalLength', SocketType.NUMBER, 50.0, '焦点长度');
    this.addInputSocket('fstop', SocketType.NUMBER, 2.8, 'F光圈');
    this.addInputSocket('maxBlur', SocketType.NUMBER, 1.0, '最大模糊');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('dofId', SocketType.STRING, '', '景深ID');
  }

  protected executeImpl(): any {
    const focusDistance = this.getInputValue('focusDistance') as number;
    const focalLength = this.getInputValue('focalLength') as number;
    const fstop = this.getInputValue('fstop') as number;
    const maxBlur = this.getInputValue('maxBlur') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');
      
      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 设置景深效果
      const result = postProcessManager.setDepthOfField({
        focusDistance,
        focalLength,
        fstop,
        maxBlur,
        enabled
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('dofId', result.dofId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 屏幕空间反射节点
 */
export class ScreenSpaceReflectionNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/ssr/screenSpaceReflection',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('intensity', SocketType.NUMBER, 1.0, '强度');
    this.addInputSocket('maxDistance', SocketType.NUMBER, 100.0, '最大距离');
    this.addInputSocket('thickness', SocketType.NUMBER, 0.1, '厚度');
    this.addInputSocket('steps', SocketType.NUMBER, 32, '步数');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');

    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('ssrId', SocketType.STRING, '', 'SSR ID');
  }

  protected executeImpl(): any {
    const intensity = this.getInputValue('intensity') as number;
    const maxDistance = this.getInputValue('maxDistance') as number;
    const thickness = this.getInputValue('thickness') as number;
    const steps = this.getInputValue('steps') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');

      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 设置屏幕空间反射
      const result = postProcessManager.setScreenSpaceReflection({
        intensity,
        maxDistance,
        thickness,
        steps,
        enabled
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('ssrId', result.ssrId || '');

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 环境光遮蔽节点
 */
export class AmbientOcclusionNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'postprocessing/ao/ambientOcclusion',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('aoType', SocketType.STRING, 'ssao', 'AO类型');
    this.addInputSocket('intensity', SocketType.NUMBER, 1.0, '强度');
    this.addInputSocket('radius', SocketType.NUMBER, 0.5, '半径');
    this.addInputSocket('bias', SocketType.NUMBER, 0.025, '偏移');
    this.addInputSocket('samples', SocketType.NUMBER, 16, '采样数');
    this.addInputSocket('enabled', SocketType.BOOLEAN, true, '启用');

    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('aoId', SocketType.STRING, '', 'AO ID');
  }

  protected executeImpl(): any {
    const aoType = this.getInputValue('aoType') as string;
    const intensity = this.getInputValue('intensity') as number;
    const radius = this.getInputValue('radius') as number;
    const bias = this.getInputValue('bias') as number;
    const samples = this.getInputValue('samples') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    try {
      const world = this.context.getWorld();
      const renderSystem = world.getSystem('RenderSystem');

      if (!renderSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '渲染系统不可用' };
      }

      // 获取后处理管理器
      const postProcessManager = renderSystem.getPostProcessManager();
      if (!postProcessManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '后处理管理器不可用' };
      }

      // 设置环境光遮蔽
      const result = postProcessManager.setAmbientOcclusion({
        type: aoType,
        intensity,
        radius,
        bias,
        samples,
        enabled
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('aoId', result.aoId || '');

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册后处理节点
 */
export function registerPostProcessingNodes(registry: NodeRegistry): void {
  // 后处理效果节点
  registry.registerNodeType({
    type: 'postprocessing/effect/postProcessEffect',
    category: NodeCategory.RENDERING,
    constructor: PostProcessEffectNode,
    label: '后处理效果',
    description: '添加后处理效果',
    tags: ['postprocessing', 'effect', 'rendering'],
    version: '1.0.0'
  });

  // 色彩调整节点
  registry.registerNodeType({
    type: 'postprocessing/color/colorAdjustment',
    category: NodeCategory.RENDERING,
    constructor: ColorAdjustmentNode,
    label: '色彩调整',
    description: '调整图像色彩属性',
    tags: ['postprocessing', 'color', 'adjustment'],
    version: '1.0.0'
  });

  // 抗锯齿节点
  registry.registerNodeType({
    type: 'postprocessing/aa/antiAliasing',
    category: NodeCategory.RENDERING,
    constructor: AntiAliasingNode,
    label: '抗锯齿',
    description: '设置抗锯齿效果',
    tags: ['postprocessing', 'antialiasing', 'quality'],
    version: '1.0.0'
  });

  // 景深效果节点
  registry.registerNodeType({
    type: 'postprocessing/dof/depthOfField',
    category: NodeCategory.RENDERING,
    constructor: DepthOfFieldNode,
    label: '景深效果',
    description: '设置景深模糊效果',
    tags: ['postprocessing', 'dof', 'blur'],
    version: '1.0.0'
  });

  // 屏幕空间反射节点
  registry.registerNodeType({
    type: 'postprocessing/ssr/screenSpaceReflection',
    category: NodeCategory.RENDERING,
    constructor: ScreenSpaceReflectionNode,
    label: '屏幕空间反射',
    description: '设置屏幕空间反射效果',
    tags: ['postprocessing', 'ssr', 'reflection'],
    version: '1.0.0'
  });

  // 环境光遮蔽节点
  registry.registerNodeType({
    type: 'postprocessing/ao/ambientOcclusion',
    category: NodeCategory.RENDERING,
    constructor: AmbientOcclusionNode,
    label: '环境光遮蔽',
    description: '设置环境光遮蔽效果',
    tags: ['postprocessing', 'ao', 'occlusion'],
    version: '1.0.0'
  });
}

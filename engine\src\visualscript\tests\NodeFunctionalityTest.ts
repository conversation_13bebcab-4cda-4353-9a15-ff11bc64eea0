/**
 * NodeFunctionalityTest.ts
 * 
 * 节点功能验证测试 - 验证关键节点的实际功能
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerOptimizedNodes } from '../presets/OptimizedNodeRegistry';

/**
 * 节点功能测试类
 */
export class NodeFunctionalityTest {
  private registry: NodeRegistry;

  constructor() {
    this.registry = new NodeRegistry();
    registerOptimizedNodes(this.registry);
  }

  /**
   * 运行功能测试
   */
  public async runFunctionalityTests(): Promise<FunctionalityReport> {
    console.log('🔧 开始运行节点功能测试...\n');

    const report: FunctionalityReport = {
      testSuites: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      timestamp: new Date()
    };

    // 测试高优先级节点
    await this.testHighPriorityNodes(report);
    
    // 测试中优先级节点
    await this.testMediumPriorityNodes(report);
    
    // 测试低优先级节点
    await this.testLowPriorityNodes(report);

    this.printFunctionalityReport(report);
    return report;
  }

  /**
   * 测试高优先级节点
   */
  private async testHighPriorityNodes(report: FunctionalityReport): Promise<void> {
    const suite: TestSuite = {
      name: '高优先级节点测试',
      tests: [],
      passed: 0,
      failed: 0
    };

    // 测试渲染系统节点
    await this.testRenderingNodes(suite);
    
    // 测试场景管理节点
    await this.testSceneManagementNodes(suite);
    
    // 测试资产管理节点
    await this.testAssetManagementNodes(suite);

    report.testSuites.push(suite);
    report.totalTests += suite.tests.length;
    report.passedTests += suite.passed;
    report.failedTests += suite.failed;
  }

  /**
   * 测试中优先级节点
   */
  private async testMediumPriorityNodes(report: FunctionalityReport): Promise<void> {
    const suite: TestSuite = {
      name: '中优先级节点测试',
      tests: [],
      passed: 0,
      failed: 0
    };

    // 测试高级动画节点
    await this.testAdvancedAnimationNodes(suite);
    
    // 测试高级UI节点
    await this.testAdvancedUINodes(suite);
    
    // 测试网络优化节点
    await this.testNetworkOptimizationNodes(suite);

    report.testSuites.push(suite);
    report.totalTests += suite.tests.length;
    report.passedTests += suite.passed;
    report.failedTests += suite.failed;
  }

  /**
   * 测试低优先级节点
   */
  private async testLowPriorityNodes(report: FunctionalityReport): Promise<void> {
    const suite: TestSuite = {
      name: '低优先级节点测试',
      tests: [],
      passed: 0,
      failed: 0
    };

    // 测试地形系统节点
    await this.testTerrainSystemNodes(suite);
    
    // 测试植被系统节点
    await this.testVegetationSystemNodes(suite);
    
    // 测试区块链系统节点
    await this.testBlockchainSystemNodes(suite);
    
    // 测试流体模拟节点
    await this.testFluidSimulationNodes(suite);

    report.testSuites.push(suite);
    report.totalTests += suite.tests.length;
    report.passedTests += suite.passed;
    report.failedTests += suite.failed;
  }

  /**
   * 测试渲染系统节点
   */
  private async testRenderingNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '设置材质节点',
        nodeType: 'rendering/material/setMaterial',
        test: async () => {
          const nodeType = this.registry.getNodeType('rendering/material/setMaterial');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_setmaterial',
            type: 'rendering/material/setMaterial'
          });
          
          // 验证插槽
          const inputSockets = node.getInputSockets();
          const outputSockets = node.getOutputSockets();
          
          if (inputSockets.length < 3) throw new Error('输入插槽数量不足');
          if (outputSockets.length < 2) throw new Error('输出插槽数量不足');
          
          return true;
        }
      },
      {
        name: '创建光源节点',
        nodeType: 'rendering/light/createLight',
        test: async () => {
          const nodeType = this.registry.getNodeType('rendering/light/createLight');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_createlight',
            type: 'rendering/light/createLight'
          });
          
          // 验证默认值
          const lightTypeSocket = node.getInputSocket('lightType');
          if (!lightTypeSocket || lightTypeSocket.defaultValue !== 'directional') {
            throw new Error('光源类型默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '渲染系统', tests);
  }

  /**
   * 测试场景管理节点
   */
  private async testSceneManagementNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '加载场景节点',
        nodeType: 'scene/loadScene',
        test: async () => {
          const nodeType = this.registry.getNodeType('scene/loadScene');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_loadscene',
            type: 'scene/loadScene'
          });
          
          // 验证异步节点特性
          if (!node.executeAsync) throw new Error('不是异步节点');
          
          return true;
        }
      },
      {
        name: '查找对象节点',
        nodeType: 'scene/findObjectByName',
        test: async () => {
          const nodeType = this.registry.getNodeType('scene/findObjectByName');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_findobject',
            type: 'scene/findObjectByName'
          });
          
          // 验证递归搜索默认值
          const recursiveSocket = node.getInputSocket('recursive');
          if (!recursiveSocket || recursiveSocket.defaultValue !== true) {
            throw new Error('递归搜索默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '场景管理', tests);
  }

  /**
   * 测试资产管理节点
   */
  private async testAssetManagementNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '加载资产节点',
        nodeType: 'asset/loadAsset',
        test: async () => {
          const nodeType = this.registry.getNodeType('asset/loadAsset');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_loadasset',
            type: 'asset/loadAsset'
          });
          
          // 验证异步节点
          if (!node.executeAsync) throw new Error('不是异步节点');
          
          return true;
        }
      },
      {
        name: '预加载资产节点',
        nodeType: 'asset/preloadAssets',
        test: async () => {
          const nodeType = this.registry.getNodeType('asset/preloadAssets');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_preloadassets',
            type: 'asset/preloadAssets'
          });
          
          // 验证并发数默认值
          const maxConcurrentSocket = node.getInputSocket('maxConcurrent');
          if (!maxConcurrentSocket || maxConcurrentSocket.defaultValue !== 4) {
            throw new Error('最大并发数默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '资产管理', tests);
  }

  /**
   * 测试高级动画节点
   */
  private async testAdvancedAnimationNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: 'IK求解器节点',
        nodeType: 'animation/ik/ikSolver',
        test: async () => {
          const nodeType = this.registry.getNodeType('animation/ik/ikSolver');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_iksolver',
            type: 'animation/ik/ikSolver'
          });
          
          // 验证迭代次数默认值
          const iterationsSocket = node.getInputSocket('iterations');
          if (!iterationsSocket || iterationsSocket.defaultValue !== 10) {
            throw new Error('迭代次数默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '高级动画', tests);
  }

  /**
   * 测试高级UI节点
   */
  private async testAdvancedUINodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '布局管理器节点',
        nodeType: 'ui/layout/layoutManager',
        test: async () => {
          const nodeType = this.registry.getNodeType('ui/layout/layoutManager');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_layoutmanager',
            type: 'ui/layout/layoutManager'
          });
          
          // 验证布局类型默认值
          const layoutTypeSocket = node.getInputSocket('layoutType');
          if (!layoutTypeSocket || layoutTypeSocket.defaultValue !== 'flex') {
            throw new Error('布局类型默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '高级UI', tests);
  }

  /**
   * 测试网络优化节点
   */
  private async testNetworkOptimizationNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: 'P2P连接节点',
        nodeType: 'network/p2p/p2pConnection',
        test: async () => {
          const nodeType = this.registry.getNodeType('network/p2p/p2pConnection');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_p2pconnection',
            type: 'network/p2p/p2pConnection'
          });
          
          // 验证超时时间默认值
          const timeoutSocket = node.getInputSocket('timeout');
          if (!timeoutSocket || timeoutSocket.defaultValue !== 30000) {
            throw new Error('超时时间默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '网络优化', tests);
  }

  /**
   * 测试地形系统节点
   */
  private async testTerrainSystemNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '地形生成器节点',
        nodeType: 'terrain/generator/terrainGenerator',
        test: async () => {
          const nodeType = this.registry.getNodeType('terrain/generator/terrainGenerator');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_terraingenerator',
            type: 'terrain/generator/terrainGenerator'
          });
          
          // 验证分辨率默认值
          const resolutionSocket = node.getInputSocket('resolution');
          if (!resolutionSocket || resolutionSocket.defaultValue !== 256) {
            throw new Error('分辨率默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '地形系统', tests);
  }

  /**
   * 测试植被系统节点
   */
  private async testVegetationSystemNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '植被放置器节点',
        nodeType: 'vegetation/placer/vegetationPlacer',
        test: async () => {
          const nodeType = this.registry.getNodeType('vegetation/placer/vegetationPlacer');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_vegetationplacer',
            type: 'vegetation/placer/vegetationPlacer'
          });
          
          // 验证密度默认值
          const densitySocket = node.getInputSocket('density');
          if (!densitySocket || densitySocket.defaultValue !== 0.5) {
            throw new Error('密度默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '植被系统', tests);
  }

  /**
   * 测试区块链系统节点
   */
  private async testBlockchainSystemNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: 'NFT管理器节点',
        nodeType: 'blockchain/nft/nftManager',
        test: async () => {
          const nodeType = this.registry.getNodeType('blockchain/nft/nftManager');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_nftmanager',
            type: 'blockchain/nft/nftManager'
          });
          
          // 验证操作类型默认值
          const actionSocket = node.getInputSocket('action');
          if (!actionSocket || actionSocket.defaultValue !== 'mint') {
            throw new Error('操作类型默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '区块链系统', tests);
  }

  /**
   * 测试流体模拟节点
   */
  private async testFluidSimulationNodes(suite: TestSuite): Promise<void> {
    const tests = [
      {
        name: '流体模拟器节点',
        nodeType: 'fluid/simulation/fluidSimulator',
        test: async () => {
          const nodeType = this.registry.getNodeType('fluid/simulation/fluidSimulator');
          if (!nodeType) throw new Error('节点类型未找到');
          
          const node = new nodeType.constructor({
            id: 'test_fluidsimulator',
            type: 'fluid/simulation/fluidSimulator'
          });
          
          // 验证粒子数量默认值
          const particleCountSocket = node.getInputSocket('particleCount');
          if (!particleCountSocket || particleCountSocket.defaultValue !== 1000) {
            throw new Error('粒子数量默认值不正确');
          }
          
          return true;
        }
      }
    ];

    await this.runTestGroup(suite, '流体模拟', tests);
  }

  /**
   * 运行测试组
   */
  private async runTestGroup(suite: TestSuite, groupName: string, tests: any[]): Promise<void> {
    console.log(`  🧪 测试组: ${groupName}`);
    
    for (const testCase of tests) {
      try {
        await testCase.test();
        suite.tests.push({
          name: testCase.name,
          nodeType: testCase.nodeType,
          passed: true,
          error: null,
          duration: 0
        });
        suite.passed++;
        console.log(`    ✅ ${testCase.name}`);
      } catch (error) {
        suite.tests.push({
          name: testCase.name,
          nodeType: testCase.nodeType,
          passed: false,
          error: error.message,
          duration: 0
        });
        suite.failed++;
        console.log(`    ❌ ${testCase.name}: ${error.message}`);
      }
    }
  }

  /**
   * 打印功能测试报告
   */
  private printFunctionalityReport(report: FunctionalityReport): void {
    console.log('\n📋 ===== 节点功能测试报告 =====');
    console.log(`🕒 测试时间: ${report.timestamp.toLocaleString()}`);
    console.log(`🧪 总测试数: ${report.totalTests}`);
    console.log(`✅ 通过: ${report.passedTests}`);
    console.log(`❌ 失败: ${report.failedTests}`);
    console.log(`📈 成功率: ${((report.passedTests / report.totalTests) * 100).toFixed(1)}%`);
    console.log('');

    for (const suite of report.testSuites) {
      console.log(`📂 ${suite.name}: ${suite.passed}/${suite.tests.length} 通过`);
      
      const failedTests = suite.tests.filter(t => !t.passed);
      if (failedTests.length > 0) {
        for (const failed of failedTests) {
          console.log(`  ❌ ${failed.name}: ${failed.error}`);
        }
      }
    }
    
    console.log('================================\n');
  }
}

// 类型定义
interface TestCase {
  name: string;
  nodeType: string;
  passed: boolean;
  error: string | null;
  duration: number;
}

interface TestSuite {
  name: string;
  tests: TestCase[];
  passed: number;
  failed: number;
}

interface FunctionalityReport {
  testSuites: TestSuite[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  timestamp: Date;
}

// 导出测试函数
export async function runNodeFunctionalityTest(): Promise<FunctionalityReport> {
  const tester = new NodeFunctionalityTest();
  return await tester.runFunctionalityTests();
}

/**
 * BlockchainSystemNodes.ts
 * 
 * 区块链系统节点 - 提供NFT管理、钱包连接、智能合约等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * NFT管理器节点
 */
export class NFTManagerNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'blockchain/nft/nftManager',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('action', SocketType.STRING, 'mint', '操作类型');
    this.addInputSocket('tokenId', SocketType.STRING, '', '代币ID');
    this.addInputSocket('metadata', SocketType.OBJECT, {}, '元数据');
    this.addInputSocket('recipient', SocketType.STRING, '', '接收者地址');
    this.addInputSocket('contractAddress', SocketType.STRING, '', '合约地址');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '操作成功');
    this.addOutputSocket('transactionHash', SocketType.STRING, '', '交易哈希');
    this.addOutputSocket('tokenId', SocketType.STRING, '', '代币ID');
    this.addOutputSocket('nftData', SocketType.OBJECT, {}, 'NFT数据');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const action = this.getInputValue('action') as string;
    const tokenId = this.getInputValue('tokenId') as string;
    const metadata = this.getInputValue('metadata') as any;
    const recipient = this.getInputValue('recipient') as string;
    const contractAddress = this.getInputValue('contractAddress') as string;

    try {
      const world = this.context.getWorld();
      const blockchainSystem = world.getSystem('BlockchainSystem');
      
      if (!blockchainSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '区块链系统不可用' };
      }

      // 获取NFT管理器
      const nftManager = blockchainSystem.getNFTManager();
      if (!nftManager) {
        this.setOutputValue('success', false);
        return { success: false, error: 'NFT管理器不可用' };
      }

      let result;
      switch (action) {
        case 'mint':
          result = await nftManager.mintNFT({
            metadata,
            recipient,
            contractAddress
          });
          break;
        case 'transfer':
          result = await nftManager.transferNFT({
            tokenId,
            recipient,
            contractAddress
          });
          break;
        case 'burn':
          result = await nftManager.burnNFT({
            tokenId,
            contractAddress
          });
          break;
        case 'get':
          result = await nftManager.getNFT({
            tokenId,
            contractAddress
          });
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的操作类型: ${action}` };
      }

      this.setOutputValue('success', result.success);
      this.setOutputValue('transactionHash', result.transactionHash || '');
      this.setOutputValue('tokenId', result.tokenId || tokenId);
      this.setOutputValue('nftData', result.nftData || {});
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 钱包连接器节点
 */
export class WalletConnectorNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'blockchain/wallet/walletConnector',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('walletType', SocketType.STRING, 'metamask', '钱包类型');
    this.addInputSocket('chainId', SocketType.NUMBER, 1, '链ID');
    this.addInputSocket('autoConnect', SocketType.BOOLEAN, false, '自动连接');
    
    // 输出插槽
    this.addOutputSocket('connected', SocketType.BOOLEAN, false, '连接状态');
    this.addOutputSocket('address', SocketType.STRING, '', '钱包地址');
    this.addOutputSocket('balance', SocketType.NUMBER, 0, '余额');
    this.addOutputSocket('chainId', SocketType.NUMBER, 0, '当前链ID');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const walletType = this.getInputValue('walletType') as string;
    const chainId = this.getInputValue('chainId') as number;
    const autoConnect = this.getInputValue('autoConnect') as boolean;

    try {
      const world = this.context.getWorld();
      const blockchainSystem = world.getSystem('BlockchainSystem');
      
      if (!blockchainSystem) {
        this.setOutputValue('connected', false);
        return { success: false, error: '区块链系统不可用' };
      }

      // 获取钱包连接器
      const walletConnector = blockchainSystem.getWalletConnector();
      if (!walletConnector) {
        this.setOutputValue('connected', false);
        return { success: false, error: '钱包连接器不可用' };
      }

      // 连接钱包
      const result = await walletConnector.connect({
        walletType,
        chainId,
        autoConnect
      });

      this.setOutputValue('connected', result.connected);
      this.setOutputValue('address', result.address || '');
      this.setOutputValue('balance', result.balance || 0);
      this.setOutputValue('chainId', result.chainId || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('connected', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 智能合约节点
 */
export class SmartContractNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'blockchain/contract/smartContract',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('contractAddress', SocketType.STRING, '', '合约地址');
    this.addInputSocket('abi', SocketType.OBJECT, [], '合约ABI');
    this.addInputSocket('method', SocketType.STRING, '', '方法名');
    this.addInputSocket('parameters', SocketType.ARRAY, [], '参数');
    this.addInputSocket('value', SocketType.NUMBER, 0, '发送金额');
    this.addInputSocket('gasLimit', SocketType.NUMBER, 0, 'Gas限制');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '调用成功');
    this.addOutputSocket('result', SocketType.ANY, null, '返回结果');
    this.addOutputSocket('transactionHash', SocketType.STRING, '', '交易哈希');
    this.addOutputSocket('gasUsed', SocketType.NUMBER, 0, '使用的Gas');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const contractAddress = this.getInputValue('contractAddress') as string;
    const abi = this.getInputValue('abi') as any[];
    const method = this.getInputValue('method') as string;
    const parameters = this.getInputValue('parameters') as any[];
    const value = this.getInputValue('value') as number;
    const gasLimit = this.getInputValue('gasLimit') as number;

    if (!contractAddress || !method) {
      this.setOutputValue('success', false);
      return { success: false, error: '合约地址和方法名不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const blockchainSystem = world.getSystem('BlockchainSystem');
      
      if (!blockchainSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '区块链系统不可用' };
      }

      // 获取智能合约管理器
      const contractManager = blockchainSystem.getContractManager();
      if (!contractManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '智能合约管理器不可用' };
      }

      // 调用智能合约
      const result = await contractManager.callContract({
        contractAddress,
        abi,
        method,
        parameters,
        value,
        gasLimit
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('result', result.result);
      this.setOutputValue('transactionHash', result.transactionHash || '');
      this.setOutputValue('gasUsed', result.gasUsed || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 代币管理节点
 */
export class TokenManagerNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'blockchain/token/tokenManager',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('action', SocketType.STRING, 'transfer', '操作类型');
    this.addInputSocket('tokenAddress', SocketType.STRING, '', '代币地址');
    this.addInputSocket('recipient', SocketType.STRING, '', '接收者');
    this.addInputSocket('amount', SocketType.NUMBER, 0, '数量');
    this.addInputSocket('spender', SocketType.STRING, '', '授权地址');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '操作成功');
    this.addOutputSocket('transactionHash', SocketType.STRING, '', '交易哈希');
    this.addOutputSocket('balance', SocketType.NUMBER, 0, '余额');
    this.addOutputSocket('allowance', SocketType.NUMBER, 0, '授权额度');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const action = this.getInputValue('action') as string;
    const tokenAddress = this.getInputValue('tokenAddress') as string;
    const recipient = this.getInputValue('recipient') as string;
    const amount = this.getInputValue('amount') as number;
    const spender = this.getInputValue('spender') as string;

    if (!tokenAddress) {
      this.setOutputValue('success', false);
      return { success: false, error: '代币地址不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const blockchainSystem = world.getSystem('BlockchainSystem');
      
      if (!blockchainSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '区块链系统不可用' };
      }

      // 获取代币管理器
      const tokenManager = blockchainSystem.getTokenManager();
      if (!tokenManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '代币管理器不可用' };
      }

      let result;
      switch (action) {
        case 'transfer':
          result = await tokenManager.transfer({
            tokenAddress,
            recipient,
            amount
          });
          break;
        case 'approve':
          result = await tokenManager.approve({
            tokenAddress,
            spender,
            amount
          });
          break;
        case 'balance':
          result = await tokenManager.getBalance({
            tokenAddress
          });
          break;
        case 'allowance':
          result = await tokenManager.getAllowance({
            tokenAddress,
            spender
          });
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的操作类型: ${action}` };
      }

      this.setOutputValue('success', result.success);
      this.setOutputValue('transactionHash', result.transactionHash || '');
      this.setOutputValue('balance', result.balance || 0);
      this.setOutputValue('allowance', result.allowance || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册区块链系统节点
 */
export function registerBlockchainSystemNodes(registry: NodeRegistry): void {
  // NFT管理节点
  registry.registerNodeType({
    type: 'blockchain/nft/nftManager',
    category: NodeCategory.CUSTOM,
    constructor: NFTManagerNode,
    label: 'NFT管理器',
    description: '管理NFT的铸造、转移和销毁',
    tags: ['blockchain', 'nft', 'token'],
    version: '1.0.0'
  });

  // 钱包连接节点
  registry.registerNodeType({
    type: 'blockchain/wallet/walletConnector',
    category: NodeCategory.CUSTOM,
    constructor: WalletConnectorNode,
    label: '钱包连接器',
    description: '连接和管理加密钱包',
    tags: ['blockchain', 'wallet', 'connection'],
    version: '1.0.0'
  });

  // 智能合约节点
  registry.registerNodeType({
    type: 'blockchain/contract/smartContract',
    category: NodeCategory.CUSTOM,
    constructor: SmartContractNode,
    label: '智能合约',
    description: '调用智能合约方法',
    tags: ['blockchain', 'contract', 'smart'],
    version: '1.0.0'
  });

  // 代币管理节点
  registry.registerNodeType({
    type: 'blockchain/token/tokenManager',
    category: NodeCategory.CUSTOM,
    constructor: TokenManagerNode,
    label: '代币管理器',
    description: '管理ERC20代币操作',
    tags: ['blockchain', 'token', 'erc20'],
    version: '1.0.0'
  });
}

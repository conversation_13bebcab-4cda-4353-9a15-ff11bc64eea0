/**
 * SceneManagementNodes.ts
 * 
 * 场景管理节点 - 提供场景加载、查询、层级管理等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 加载场景节点
 */
export class LoadSceneNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/loadScene',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('scenePath', SocketType.STRING, '', '场景路径');
    this.addInputSocket('loadMode', SocketType.STRING, 'additive', '加载模式');
    this.addInputSocket('async', SocketType.BOOLEAN, true, '异步加载');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '加载成功');
    this.addOutputSocket('sceneRoot', SocketType.ENTITY, null, '场景根实体');
    this.addOutputSocket('loadedEntities', SocketType.ARRAY, [], '已加载实体');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const scenePath = this.getInputValue('scenePath') as string;
    const loadMode = this.getInputValue('loadMode') as string;
    const isAsync = this.getInputValue('async') as boolean;

    if (!scenePath) {
      this.setOutputValue('success', false);
      return { success: false, error: '场景路径不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const sceneManager = world.getSystem('SceneManager');
      
      if (!sceneManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '场景管理器不可用' };
      }

      let result;
      if (isAsync) {
        result = await sceneManager.loadSceneAsync(scenePath, loadMode);
      } else {
        result = sceneManager.loadScene(scenePath, loadMode);
      }

      if (result.success) {
        this.setOutputValue('success', true);
        this.setOutputValue('sceneRoot', result.sceneRoot);
        this.setOutputValue('loadedEntities', result.loadedEntities || []);
      } else {
        this.setOutputValue('success', false);
      }
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 卸载场景节点
 */
export class UnloadSceneNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/unloadScene',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('sceneRoot', SocketType.ENTITY, null, '场景根实体');
    this.addInputSocket('destroyEntities', SocketType.BOOLEAN, true, '销毁实体');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '卸载成功');
  }

  protected executeImpl(): any {
    const sceneRoot = this.getInputValue('sceneRoot') as Entity;
    const destroyEntities = this.getInputValue('destroyEntities') as boolean;

    if (!sceneRoot) {
      this.setOutputValue('success', false);
      return { success: false, error: '场景根实体不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const sceneManager = world.getSystem('SceneManager');
      
      if (!sceneManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '场景管理器不可用' };
      }

      const result = sceneManager.unloadScene(sceneRoot, destroyEntities);
      this.setOutputValue('success', result.success);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取场景对象节点
 */
export class GetSceneObjectNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/getSceneObject',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('sceneRoot', SocketType.ENTITY, null, '场景根实体');
    this.addInputSocket('path', SocketType.STRING, '', '对象路径');
    
    // 输出插槽
    this.addOutputSocket('entity', SocketType.ENTITY, null, '找到的实体');
    this.addOutputSocket('found', SocketType.BOOLEAN, false, '是否找到');
  }

  protected executeImpl(): any {
    const sceneRoot = this.getInputValue('sceneRoot') as Entity;
    const path = this.getInputValue('path') as string;

    if (!sceneRoot) {
      this.setOutputValue('found', false);
      return { found: false, error: '场景根实体不能为空' };
    }

    if (!path) {
      this.setOutputValue('found', false);
      return { found: false, error: '对象路径不能为空' };
    }

    try {
      // 解析路径并查找实体
      const pathParts = path.split('/').filter(part => part.length > 0);
      let currentEntity = sceneRoot;

      for (const part of pathParts) {
        const children = this.getEntityChildren(currentEntity);
        const child = children.find(child => child.name === part);
        
        if (!child) {
          this.setOutputValue('found', false);
          this.setOutputValue('entity', null);
          return { found: false, error: `找不到路径: ${path}` };
        }
        
        currentEntity = child;
      }

      this.setOutputValue('entity', currentEntity);
      this.setOutputValue('found', true);
      
      return { found: true, entity: currentEntity };
    } catch (error) {
      this.setOutputValue('found', false);
      return { found: false, error: error.message };
    }
  }

  private getEntityChildren(entity: Entity): Entity[] {
    // 获取实体的子实体
    const transformComponent = entity.getComponent('TransformComponent');
    if (transformComponent && transformComponent.getChildren) {
      return transformComponent.getChildren();
    }
    return [];
  }
}

/**
 * 按名称查找对象节点
 */
export class FindObjectByNameNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/findObjectByName',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('name', SocketType.STRING, '', '对象名称');
    this.addInputSocket('searchRoot', SocketType.ENTITY, null, '搜索根实体');
    this.addInputSocket('recursive', SocketType.BOOLEAN, true, '递归搜索');
    
    // 输出插槽
    this.addOutputSocket('entity', SocketType.ENTITY, null, '找到的实体');
    this.addOutputSocket('entities', SocketType.ARRAY, [], '所有匹配的实体');
    this.addOutputSocket('found', SocketType.BOOLEAN, false, '是否找到');
  }

  protected executeImpl(): any {
    const name = this.getInputValue('name') as string;
    const searchRoot = this.getInputValue('searchRoot') as Entity;
    const recursive = this.getInputValue('recursive') as boolean;

    if (!name) {
      this.setOutputValue('found', false);
      return { found: false, error: '对象名称不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const rootEntity = searchRoot || world.getRootEntity();
      
      const foundEntities = this.searchEntitiesByName(rootEntity, name, recursive);
      
      this.setOutputValue('entities', foundEntities);
      this.setOutputValue('found', foundEntities.length > 0);
      this.setOutputValue('entity', foundEntities.length > 0 ? foundEntities[0] : null);
      
      return { 
        found: foundEntities.length > 0, 
        entities: foundEntities,
        entity: foundEntities.length > 0 ? foundEntities[0] : null
      };
    } catch (error) {
      this.setOutputValue('found', false);
      return { found: false, error: error.message };
    }
  }

  private searchEntitiesByName(entity: Entity, name: string, recursive: boolean): Entity[] {
    const results: Entity[] = [];
    
    // 检查当前实体
    if (entity.name === name) {
      results.push(entity);
    }
    
    // 递归搜索子实体
    if (recursive) {
      const children = this.getEntityChildren(entity);
      for (const child of children) {
        results.push(...this.searchEntitiesByName(child, name, recursive));
      }
    }
    
    return results;
  }

  private getEntityChildren(entity: Entity): Entity[] {
    // 获取实体的子实体
    const transformComponent = entity.getComponent('TransformComponent');
    if (transformComponent && transformComponent.getChildren) {
      return transformComponent.getChildren();
    }
    return [];
  }
}

/**
 * 设置对象父级节点
 */
export class SetObjectParentNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/setObjectParent',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '子实体');
    this.addInputSocket('parent', SocketType.ENTITY, null, '父实体');
    this.addInputSocket('keepWorldTransform', SocketType.BOOLEAN, true, '保持世界变换');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('entity', SocketType.ENTITY, null, '子实体');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const parent = this.getInputValue('parent') as Entity;
    const keepWorldTransform = this.getInputValue('keepWorldTransform') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '子实体不能为空' };
    }

    try {
      const entityTransform = entity.getComponent('TransformComponent');
      if (!entityTransform) {
        this.setOutputValue('success', false);
        return { success: false, error: '子实体没有变换组件' };
      }

      if (parent) {
        const parentTransform = parent.getComponent('TransformComponent');
        if (!parentTransform) {
          this.setOutputValue('success', false);
          return { success: false, error: '父实体没有变换组件' };
        }
        
        entityTransform.setParent(parentTransform, keepWorldTransform);
      } else {
        // 设置为根实体
        entityTransform.setParent(null, keepWorldTransform);
      }

      this.setOutputValue('success', true);
      this.setOutputValue('entity', entity);
      
      return { success: true };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取对象子级节点
 */
export class GetObjectChildrenNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'scene/getObjectChildren',
      category: NodeCategory.ENTITY
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '父实体');
    this.addInputSocket('recursive', SocketType.BOOLEAN, false, '递归获取');

    // 输出插槽
    this.addOutputSocket('children', SocketType.ARRAY, [], '子实体数组');
    this.addOutputSocket('count', SocketType.NUMBER, 0, '子实体数量');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const recursive = this.getInputValue('recursive') as boolean;

    if (!entity) {
      this.setOutputValue('children', []);
      this.setOutputValue('count', 0);
      return { children: [], count: 0, error: '实体不能为空' };
    }

    try {
      let children: Entity[] = [];

      if (recursive) {
        children = this.getAllDescendants(entity);
      } else {
        children = this.getEntityChildren(entity);
      }

      this.setOutputValue('children', children);
      this.setOutputValue('count', children.length);

      return { children, count: children.length };
    } catch (error) {
      this.setOutputValue('children', []);
      this.setOutputValue('count', 0);
      return { children: [], count: 0, error: error.message };
    }
  }

  private getEntityChildren(entity: Entity): Entity[] {
    const transformComponent = entity.getComponent('TransformComponent');
    if (transformComponent && transformComponent.getChildren) {
      return transformComponent.getChildren();
    }
    return [];
  }

  private getAllDescendants(entity: Entity): Entity[] {
    const descendants: Entity[] = [];
    const children = this.getEntityChildren(entity);

    for (const child of children) {
      descendants.push(child);
      descendants.push(...this.getAllDescendants(child));
    }

    return descendants;
  }
}

/**
 * 注册场景管理节点
 */
export function registerSceneManagementNodes(registry: NodeRegistry): void {
  // 场景加载节点
  registry.registerNodeType({
    type: 'scene/loadScene',
    category: NodeCategory.ENTITY,
    constructor: LoadSceneNode,
    label: '加载场景',
    description: '加载场景文件',
    tags: ['scene', 'load'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'scene/unloadScene',
    category: NodeCategory.ENTITY,
    constructor: UnloadSceneNode,
    label: '卸载场景',
    description: '卸载场景并清理资源',
    tags: ['scene', 'unload'],
    version: '1.0.0'
  });

  // 场景查询节点
  registry.registerNodeType({
    type: 'scene/getSceneObject',
    category: NodeCategory.ENTITY,
    constructor: GetSceneObjectNode,
    label: '获取场景对象',
    description: '通过路径获取场景中的对象',
    tags: ['scene', 'query'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'scene/findObjectByName',
    category: NodeCategory.ENTITY,
    constructor: FindObjectByNameNode,
    label: '按名称查找对象',
    description: '在场景中按名称查找对象',
    tags: ['scene', 'find'],
    version: '1.0.0'
  });

  // 层级管理节点
  registry.registerNodeType({
    type: 'scene/setObjectParent',
    category: NodeCategory.ENTITY,
    constructor: SetObjectParentNode,
    label: '设置对象父级',
    description: '设置对象的父级实体',
    tags: ['scene', 'hierarchy'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'scene/getObjectChildren',
    category: NodeCategory.ENTITY,
    constructor: GetObjectChildrenNode,
    label: '获取对象子级',
    description: '获取对象的子级实体',
    tags: ['scene', 'hierarchy'],
    version: '1.0.0'
  });
}

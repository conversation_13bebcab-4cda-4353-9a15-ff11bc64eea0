/**
 * VegetationSystemNodes.ts
 * 
 * 植被系统节点 - 提供植被放置、生态系统管理、风力效果等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 植被放置器节点
 */
export class VegetationPlacerNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'vegetation/placer/vegetationPlacer',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('terrain', SocketType.ENTITY, null, '地形实体');
    this.addInputSocket('vegetationType', SocketType.STRING, 'grass', '植被类型');
    this.addInputSocket('density', SocketType.NUMBER, 0.5, '密度');
    this.addInputSocket('minScale', SocketType.NUMBER, 0.8, '最小缩放');
    this.addInputSocket('maxScale', SocketType.NUMBER, 1.2, '最大缩放');
    this.addInputSocket('slopeRange', SocketType.VECTOR2, { x: 0, y: 30 }, '坡度范围');
    this.addInputSocket('heightRange', SocketType.VECTOR2, { x: 0, y: 100 }, '高度范围');
    this.addInputSocket('seed', SocketType.NUMBER, 12345, '随机种子');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '放置成功');
    this.addOutputSocket('vegetationCount', SocketType.NUMBER, 0, '植被数量');
    this.addOutputSocket('vegetationGroup', SocketType.ENTITY, null, '植被组');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const terrain = this.getInputValue('terrain') as Entity;
    const vegetationType = this.getInputValue('vegetationType') as string;
    const density = this.getInputValue('density') as number;
    const minScale = this.getInputValue('minScale') as number;
    const maxScale = this.getInputValue('maxScale') as number;
    const slopeRange = this.getInputValue('slopeRange') as { x: number, y: number };
    const heightRange = this.getInputValue('heightRange') as { x: number, y: number };
    const seed = this.getInputValue('seed') as number;

    if (!terrain) {
      this.setOutputValue('success', false);
      return { success: false, error: '地形实体不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const vegetationSystem = world.getSystem('VegetationSystem');
      
      if (!vegetationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '植被系统不可用' };
      }

      // 放置植被
      const result = await vegetationSystem.placeVegetation({
        terrain,
        vegetationType,
        density,
        minScale,
        maxScale,
        slopeRange,
        heightRange,
        seed
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('vegetationCount', result.vegetationCount || 0);
      this.setOutputValue('vegetationGroup', result.vegetationGroup);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 生态系统管理器节点
 */
export class EcosystemManagerNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'vegetation/ecosystem/ecosystemManager',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('vegetationGroup', SocketType.ENTITY, null, '植被组');
    this.addInputSocket('growthRate', SocketType.NUMBER, 1.0, '生长速率');
    this.addInputSocket('seasonalEffect', SocketType.BOOLEAN, true, '季节效果');
    this.addInputSocket('weatherEffect', SocketType.BOOLEAN, true, '天气效果');
    this.addInputSocket('competitionEnabled', SocketType.BOOLEAN, true, '竞争机制');
    this.addInputSocket('maxAge', SocketType.NUMBER, 100, '最大年龄');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('ecosystemId', SocketType.STRING, '', '生态系统ID');
    this.addOutputSocket('healthIndex', SocketType.NUMBER, 1.0, '健康指数');
  }

  protected executeImpl(): any {
    const vegetationGroup = this.getInputValue('vegetationGroup') as Entity;
    const growthRate = this.getInputValue('growthRate') as number;
    const seasonalEffect = this.getInputValue('seasonalEffect') as boolean;
    const weatherEffect = this.getInputValue('weatherEffect') as boolean;
    const competitionEnabled = this.getInputValue('competitionEnabled') as boolean;
    const maxAge = this.getInputValue('maxAge') as number;

    if (!vegetationGroup) {
      this.setOutputValue('success', false);
      return { success: false, error: '植被组不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const vegetationSystem = world.getSystem('VegetationSystem');
      
      if (!vegetationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '植被系统不可用' };
      }

      // 创建生态系统
      const result = vegetationSystem.createEcosystem({
        vegetationGroup,
        growthRate,
        seasonalEffect,
        weatherEffect,
        competitionEnabled,
        maxAge
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('ecosystemId', result.ecosystemId || '');
      this.setOutputValue('healthIndex', result.healthIndex || 1.0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 风力效果节点
 */
export class WindEffectNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'vegetation/effects/windEffect',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('vegetationGroup', SocketType.ENTITY, null, '植被组');
    this.addInputSocket('windDirection', SocketType.VECTOR3, { x: 1, y: 0, z: 0 }, '风向');
    this.addInputSocket('windStrength', SocketType.NUMBER, 1.0, '风力强度');
    this.addInputSocket('gustiness', SocketType.NUMBER, 0.5, '阵风强度');
    this.addInputSocket('frequency', SocketType.NUMBER, 1.0, '频率');
    this.addInputSocket('enableTurbulence', SocketType.BOOLEAN, true, '启用湍流');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('windZoneId', SocketType.STRING, '', '风区ID');
  }

  protected executeImpl(): any {
    const vegetationGroup = this.getInputValue('vegetationGroup') as Entity;
    const windDirection = this.getInputValue('windDirection') as { x: number, y: number, z: number };
    const windStrength = this.getInputValue('windStrength') as number;
    const gustiness = this.getInputValue('gustiness') as number;
    const frequency = this.getInputValue('frequency') as number;
    const enableTurbulence = this.getInputValue('enableTurbulence') as boolean;

    if (!vegetationGroup) {
      this.setOutputValue('success', false);
      return { success: false, error: '植被组不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const vegetationSystem = world.getSystem('VegetationSystem');
      
      if (!vegetationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '植被系统不可用' };
      }

      // 设置风力效果
      const result = vegetationSystem.setWindEffect({
        vegetationGroup,
        windDirection,
        windStrength,
        gustiness,
        frequency,
        enableTurbulence
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('windZoneId', result.windZoneId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 植被生长节点
 */
export class VegetationGrowthNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'vegetation/growth/vegetationGrowth',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('vegetation', SocketType.ENTITY, null, '植被实体');
    this.addInputSocket('growthStage', SocketType.NUMBER, 0, '生长阶段');
    this.addInputSocket('growthSpeed', SocketType.NUMBER, 1.0, '生长速度');
    this.addInputSocket('maxSize', SocketType.NUMBER, 1.0, '最大尺寸');
    this.addInputSocket('healthFactor', SocketType.NUMBER, 1.0, '健康因子');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('currentStage', SocketType.NUMBER, 0, '当前阶段');
    this.addOutputSocket('currentSize', SocketType.NUMBER, 0, '当前尺寸');
  }

  protected executeImpl(): any {
    const vegetation = this.getInputValue('vegetation') as Entity;
    const growthStage = this.getInputValue('growthStage') as number;
    const growthSpeed = this.getInputValue('growthSpeed') as number;
    const maxSize = this.getInputValue('maxSize') as number;
    const healthFactor = this.getInputValue('healthFactor') as number;

    if (!vegetation) {
      this.setOutputValue('success', false);
      return { success: false, error: '植被实体不能为空' };
    }

    try {
      // 获取植被组件
      const vegetationComponent = vegetation.getComponent('VegetationComponent');
      if (!vegetationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有植被组件' };
      }

      // 设置生长参数
      const result = vegetationComponent.setGrowthParameters({
        growthStage,
        growthSpeed,
        maxSize,
        healthFactor
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('currentStage', result.currentStage || 0);
      this.setOutputValue('currentSize', result.currentSize || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册植被系统节点
 */
export function registerVegetationSystemNodes(registry: NodeRegistry): void {
  // 植被放置节点
  registry.registerNodeType({
    type: 'vegetation/placer/vegetationPlacer',
    category: NodeCategory.CUSTOM,
    constructor: VegetationPlacerNode,
    label: '植被放置器',
    description: '在地形上放置植被',
    tags: ['vegetation', 'placer', 'terrain'],
    version: '1.0.0'
  });

  // 生态系统管理节点
  registry.registerNodeType({
    type: 'vegetation/ecosystem/ecosystemManager',
    category: NodeCategory.CUSTOM,
    constructor: EcosystemManagerNode,
    label: '生态系统管理器',
    description: '管理植被生态系统',
    tags: ['vegetation', 'ecosystem', 'management'],
    version: '1.0.0'
  });

  // 环境效果节点
  registry.registerNodeType({
    type: 'vegetation/effects/windEffect',
    category: NodeCategory.CUSTOM,
    constructor: WindEffectNode,
    label: '风力效果',
    description: '为植被添加风力效果',
    tags: ['vegetation', 'wind', 'effect'],
    version: '1.0.0'
  });

  // 植被生长节点
  registry.registerNodeType({
    type: 'vegetation/growth/vegetationGrowth',
    category: NodeCategory.CUSTOM,
    constructor: VegetationGrowthNode,
    label: '植被生长',
    description: '控制植被生长过程',
    tags: ['vegetation', 'growth'],
    version: '1.0.0'
  });

  // 季节效果节点
  registry.registerNodeType({
    type: 'vegetation/season/vegetationSeason',
    category: NodeCategory.CUSTOM,
    constructor: VegetationSeasonNode,
    label: '植被季节效果',
    description: '设置植被的季节变化',
    tags: ['vegetation', 'season', 'weather'],
    version: '1.0.0'
  });
}

/**
 * 植被季节效果节点
 */
export class VegetationSeasonNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'vegetation/season/vegetationSeason',
      category: NodeCategory.CUSTOM
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('vegetationGroup', SocketType.ENTITY, null, '植被组');
    this.addInputSocket('season', SocketType.STRING, 'spring', '季节');
    this.addInputSocket('temperature', SocketType.NUMBER, 20, '温度');
    this.addInputSocket('humidity', SocketType.NUMBER, 0.6, '湿度');
    this.addInputSocket('transitionSpeed', SocketType.NUMBER, 1.0, '过渡速度');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('seasonalState', SocketType.OBJECT, {}, '季节状态');
  }

  protected executeImpl(): any {
    const vegetationGroup = this.getInputValue('vegetationGroup') as Entity;
    const season = this.getInputValue('season') as string;
    const temperature = this.getInputValue('temperature') as number;
    const humidity = this.getInputValue('humidity') as number;
    const transitionSpeed = this.getInputValue('transitionSpeed') as number;

    if (!vegetationGroup) {
      this.setOutputValue('success', false);
      return { success: false, error: '植被组不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const vegetationSystem = world.getSystem('VegetationSystem');
      
      if (!vegetationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '植被系统不可用' };
      }

      // 设置季节效果
      const result = vegetationSystem.setSeasonalEffect({
        vegetationGroup,
        season,
        temperature,
        humidity,
        transitionSpeed
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('seasonalState', result.seasonalState || {});
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

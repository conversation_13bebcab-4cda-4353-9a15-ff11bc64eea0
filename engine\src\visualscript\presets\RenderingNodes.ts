/**
 * RenderingNodes.ts
 * 
 * 渲染系统节点 - 提供材质、光照、相机等渲染相关功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 设置材质节点
 */
export class SetMaterialNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/material/setMaterial',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '实体');
    this.addInputSocket('materialId', SocketType.STRING, '', '材质ID');
    this.addInputSocket('materialData', SocketType.OBJECT, {}, '材质数据');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('entity', SocketType.ENTITY, null, '实体');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const materialId = this.getInputValue('materialId') as string;
    const materialData = this.getInputValue('materialData') as any;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取渲染组件
      const renderComponent = entity.getComponent('RenderComponent');
      if (!renderComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有渲染组件' };
      }

      // 设置材质
      if (materialId) {
        renderComponent.setMaterial(materialId, materialData);
      }

      this.setOutputValue('success', true);
      this.setOutputValue('entity', entity);
      
      return { success: true };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取材质节点
 */
export class GetMaterialNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/material/getMaterial',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '实体');
    
    // 输出插槽
    this.addOutputSocket('materialId', SocketType.STRING, '', '材质ID');
    this.addOutputSocket('materialData', SocketType.OBJECT, {}, '材质数据');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '获取成功');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取渲染组件
      const renderComponent = entity.getComponent('RenderComponent');
      if (!renderComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有渲染组件' };
      }

      // 获取材质信息
      const materialInfo = renderComponent.getMaterial();
      
      this.setOutputValue('materialId', materialInfo?.id || '');
      this.setOutputValue('materialData', materialInfo?.data || {});
      this.setOutputValue('success', true);
      
      return { success: true, materialInfo };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 创建光源节点
 */
export class CreateLightNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/light/createLight',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('lightType', SocketType.STRING, 'directional', '光源类型');
    this.addInputSocket('color', SocketType.VECTOR3, { x: 1, y: 1, z: 1 }, '光源颜色');
    this.addInputSocket('intensity', SocketType.NUMBER, 1.0, '光源强度');
    this.addInputSocket('position', SocketType.VECTOR3, { x: 0, y: 10, z: 0 }, '光源位置');
    this.addInputSocket('direction', SocketType.VECTOR3, { x: 0, y: -1, z: 0 }, '光源方向');
    
    // 输出插槽
    this.addOutputSocket('lightEntity', SocketType.ENTITY, null, '光源实体');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '创建成功');
  }

  protected executeImpl(): any {
    const lightType = this.getInputValue('lightType') as string;
    const color = this.getInputValue('color') as { x: number, y: number, z: number };
    const intensity = this.getInputValue('intensity') as number;
    const position = this.getInputValue('position') as { x: number, y: number, z: number };
    const direction = this.getInputValue('direction') as { x: number, y: number, z: number };

    try {
      // 创建光源实体
      const world = this.context.getWorld();
      const lightEntity = world.createEntity(`Light_${Date.now()}`);
      
      // 添加变换组件
      const transformComponent = lightEntity.addComponent('TransformComponent');
      transformComponent.setPosition(position.x, position.y, position.z);
      
      // 添加光源组件
      const lightComponent = lightEntity.addComponent('LightComponent');
      lightComponent.setType(lightType);
      lightComponent.setColor(color.x, color.y, color.z);
      lightComponent.setIntensity(intensity);
      lightComponent.setDirection(direction.x, direction.y, direction.z);

      this.setOutputValue('lightEntity', lightEntity);
      this.setOutputValue('success', true);
      
      return { success: true, lightEntity };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 设置光源属性节点
 */
export class SetLightPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/light/setLightProperty',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('lightEntity', SocketType.ENTITY, null, '光源实体');
    this.addInputSocket('property', SocketType.STRING, 'intensity', '属性名称');
    this.addInputSocket('value', SocketType.ANY, null, '属性值');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('lightEntity', SocketType.ENTITY, null, '光源实体');
  }

  protected executeImpl(): any {
    const lightEntity = this.getInputValue('lightEntity') as Entity;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value');

    if (!lightEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '光源实体不能为空' };
    }

    try {
      // 获取光源组件
      const lightComponent = lightEntity.getComponent('LightComponent');
      if (!lightComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有光源组件' };
      }

      // 设置属性
      switch (property) {
        case 'intensity':
          lightComponent.setIntensity(value as number);
          break;
        case 'color':
          const color = value as { x: number, y: number, z: number };
          lightComponent.setColor(color.x, color.y, color.z);
          break;
        case 'type':
          lightComponent.setType(value as string);
          break;
        case 'direction':
          const direction = value as { x: number, y: number, z: number };
          lightComponent.setDirection(direction.x, direction.y, direction.z);
          break;
        case 'range':
          lightComponent.setRange(value as number);
          break;
        case 'spotAngle':
          lightComponent.setSpotAngle(value as number);
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的光源属性: ${property}` };
      }

      this.setOutputValue('success', true);
      this.setOutputValue('lightEntity', lightEntity);
      
      return { success: true };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 设置相机属性节点
 */
export class SetCameraPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/camera/setCameraProperty',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('cameraEntity', SocketType.ENTITY, null, '相机实体');
    this.addInputSocket('property', SocketType.STRING, 'fov', '属性名称');
    this.addInputSocket('value', SocketType.ANY, null, '属性值');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('cameraEntity', SocketType.ENTITY, null, '相机实体');
  }

  protected executeImpl(): any {
    const cameraEntity = this.getInputValue('cameraEntity') as Entity;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value');

    if (!cameraEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '相机实体不能为空' };
    }

    try {
      // 获取相机组件
      const cameraComponent = cameraEntity.getComponent('CameraComponent');
      if (!cameraComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有相机组件' };
      }

      // 设置属性
      switch (property) {
        case 'fov':
          cameraComponent.setFOV(value as number);
          break;
        case 'near':
          cameraComponent.setNear(value as number);
          break;
        case 'far':
          cameraComponent.setFar(value as number);
          break;
        case 'aspect':
          cameraComponent.setAspect(value as number);
          break;
        case 'position':
          const position = value as { x: number, y: number, z: number };
          const transformComponent = cameraEntity.getComponent('TransformComponent');
          if (transformComponent) {
            transformComponent.setPosition(position.x, position.y, position.z);
          }
          break;
        case 'target':
          const target = value as { x: number, y: number, z: number };
          cameraComponent.setTarget(target.x, target.y, target.z);
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的相机属性: ${property}` };
      }

      this.setOutputValue('success', true);
      this.setOutputValue('cameraEntity', cameraEntity);
      
      return { success: true };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 获取相机属性节点
 */
export class GetCameraPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/camera/getCameraProperty',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('cameraEntity', SocketType.ENTITY, null, '相机实体');
    this.addInputSocket('property', SocketType.STRING, 'fov', '属性名称');

    // 输出插槽
    this.addOutputSocket('value', SocketType.ANY, null, '属性值');
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '获取成功');
  }

  protected executeImpl(): any {
    const cameraEntity = this.getInputValue('cameraEntity') as Entity;
    const property = this.getInputValue('property') as string;

    if (!cameraEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '相机实体不能为空' };
    }

    try {
      // 获取相机组件
      const cameraComponent = cameraEntity.getComponent('CameraComponent');
      if (!cameraComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有相机组件' };
      }

      let value: any;

      // 获取属性
      switch (property) {
        case 'fov':
          value = cameraComponent.getFOV();
          break;
        case 'near':
          value = cameraComponent.getNear();
          break;
        case 'far':
          value = cameraComponent.getFar();
          break;
        case 'aspect':
          value = cameraComponent.getAspect();
          break;
        case 'position':
          const transformComponent = cameraEntity.getComponent('TransformComponent');
          value = transformComponent ? transformComponent.getPosition() : { x: 0, y: 0, z: 0 };
          break;
        case 'target':
          value = cameraComponent.getTarget();
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的相机属性: ${property}` };
      }

      this.setOutputValue('value', value);
      this.setOutputValue('success', true);

      return { success: true, value };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 设置渲染属性节点
 */
export class SetRenderPropertyNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'rendering/setRenderProperty',
      category: NodeCategory.RENDERING
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '实体');
    this.addInputSocket('property', SocketType.STRING, 'visible', '属性名称');
    this.addInputSocket('value', SocketType.ANY, null, '属性值');

    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('entity', SocketType.ENTITY, null, '实体');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const property = this.getInputValue('property') as string;
    const value = this.getInputValue('value');

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取渲染组件
      const renderComponent = entity.getComponent('RenderComponent');
      if (!renderComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有渲染组件' };
      }

      // 设置属性
      switch (property) {
        case 'visible':
          renderComponent.setVisible(value as boolean);
          break;
        case 'castShadow':
          renderComponent.setCastShadow(value as boolean);
          break;
        case 'receiveShadow':
          renderComponent.setReceiveShadow(value as boolean);
          break;
        case 'renderOrder':
          renderComponent.setRenderOrder(value as number);
          break;
        case 'opacity':
          renderComponent.setOpacity(value as number);
          break;
        case 'wireframe':
          renderComponent.setWireframe(value as boolean);
          break;
        default:
          this.setOutputValue('success', false);
          return { success: false, error: `未知的渲染属性: ${property}` };
      }

      this.setOutputValue('success', true);
      this.setOutputValue('entity', entity);

      return { success: true };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册渲染系统节点
 */
export function registerRenderingNodes(registry: NodeRegistry): void {
  // 材质节点
  registry.registerNodeType({
    type: 'rendering/material/setMaterial',
    category: NodeCategory.RENDERING,
    constructor: SetMaterialNode,
    label: '设置材质',
    description: '为实体设置材质',
    tags: ['rendering', 'material'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'rendering/material/getMaterial',
    category: NodeCategory.RENDERING,
    constructor: GetMaterialNode,
    label: '获取材质',
    description: '获取实体的材质信息',
    tags: ['rendering', 'material'],
    version: '1.0.0'
  });

  // 光照节点
  registry.registerNodeType({
    type: 'rendering/light/createLight',
    category: NodeCategory.RENDERING,
    constructor: CreateLightNode,
    label: '创建光源',
    description: '创建新的光源实体',
    tags: ['rendering', 'light'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'rendering/light/setLightProperty',
    category: NodeCategory.RENDERING,
    constructor: SetLightPropertyNode,
    label: '设置光源属性',
    description: '设置光源的属性',
    tags: ['rendering', 'light'],
    version: '1.0.0'
  });

  // 相机节点
  registry.registerNodeType({
    type: 'rendering/camera/setCameraProperty',
    category: NodeCategory.RENDERING,
    constructor: SetCameraPropertyNode,
    label: '设置相机属性',
    description: '设置相机的属性',
    tags: ['rendering', 'camera'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'rendering/camera/getCameraProperty',
    category: NodeCategory.RENDERING,
    constructor: GetCameraPropertyNode,
    label: '获取相机属性',
    description: '获取相机的属性值',
    tags: ['rendering', 'camera'],
    version: '1.0.0'
  });

  // 渲染属性节点
  registry.registerNodeType({
    type: 'rendering/setRenderProperty',
    category: NodeCategory.RENDERING,
    constructor: SetRenderPropertyNode,
    label: '设置渲染属性',
    description: '设置实体的渲染属性',
    tags: ['rendering', 'property'],
    version: '1.0.0'
  });
}

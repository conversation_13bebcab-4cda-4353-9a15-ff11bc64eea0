/**
 * NetworkOptimizationNodes.ts
 * 
 * 网络优化节点 - 提供P2P连接、网络优化、带宽管理等功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * P2P连接节点
 */
export class P2PConnectionNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'network/p2p/p2pConnection',
      category: NodeCategory.NETWORK
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('peerId', SocketType.STRING, '', '对等节点ID');
    this.addInputSocket('connectionType', SocketType.STRING, 'data', '连接类型');
    this.addInputSocket('config', SocketType.OBJECT, {}, '连接配置');
    this.addInputSocket('timeout', SocketType.NUMBER, 30000, '超时时间(ms)');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '连接成功');
    this.addOutputSocket('connection', SocketType.OBJECT, null, '连接对象');
    this.addOutputSocket('connectionId', SocketType.STRING, '', '连接ID');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const peerId = this.getInputValue('peerId') as string;
    const connectionType = this.getInputValue('connectionType') as string;
    const config = this.getInputValue('config') as any;
    const timeout = this.getInputValue('timeout') as number;

    if (!peerId) {
      this.setOutputValue('success', false);
      return { success: false, error: '对等节点ID不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const networkSystem = world.getSystem('NetworkSystem');
      
      if (!networkSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '网络系统不可用' };
      }

      // 获取P2P管理器
      const p2pManager = networkSystem.getP2PManager();
      if (!p2pManager) {
        this.setOutputValue('success', false);
        return { success: false, error: 'P2P管理器不可用' };
      }

      // 建立P2P连接
      const result = await p2pManager.connect({
        peerId,
        connectionType,
        config,
        timeout
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('connection', result.connection);
      this.setOutputValue('connectionId', result.connectionId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * P2P数据通道节点
 */
export class P2PDataChannelNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'network/p2p/p2pDataChannel',
      category: NodeCategory.NETWORK
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('connection', SocketType.OBJECT, null, 'P2P连接');
    this.addInputSocket('channelName', SocketType.STRING, 'data', '通道名称');
    this.addInputSocket('data', SocketType.ANY, null, '发送数据');
    this.addInputSocket('reliable', SocketType.BOOLEAN, true, '可靠传输');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '发送成功');
    this.addOutputSocket('channel', SocketType.OBJECT, null, '数据通道');
  }

  protected executeImpl(): any {
    const connection = this.getInputValue('connection') as any;
    const channelName = this.getInputValue('channelName') as string;
    const data = this.getInputValue('data');
    const reliable = this.getInputValue('reliable') as boolean;

    if (!connection) {
      this.setOutputValue('success', false);
      return { success: false, error: 'P2P连接不能为空' };
    }

    try {
      // 获取或创建数据通道
      let channel = connection.getDataChannel(channelName);
      if (!channel) {
        channel = connection.createDataChannel(channelName, {
          ordered: reliable,
          maxRetransmits: reliable ? 3 : 0
        });
      }

      // 发送数据
      if (data !== null && data !== undefined) {
        const result = channel.send(data);
        this.setOutputValue('success', result.success);
      } else {
        this.setOutputValue('success', true);
      }

      this.setOutputValue('channel', channel);
      
      return { success: true, channel };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 网络优化器节点
 */
export class NetworkOptimizerNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'network/optimization/networkOptimizer',
      category: NodeCategory.NETWORK
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('optimizationType', SocketType.STRING, 'adaptive', '优化类型');
    this.addInputSocket('targetLatency', SocketType.NUMBER, 50, '目标延迟(ms)');
    this.addInputSocket('maxBandwidth', SocketType.NUMBER, 1000, '最大带宽(kbps)');
    this.addInputSocket('compressionLevel', SocketType.NUMBER, 5, '压缩级别');
    this.addInputSocket('enablePrediction', SocketType.BOOLEAN, true, '启用预测');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '优化成功');
    this.addOutputSocket('optimizerId', SocketType.STRING, '', '优化器ID');
    this.addOutputSocket('currentLatency', SocketType.NUMBER, 0, '当前延迟');
    this.addOutputSocket('currentBandwidth', SocketType.NUMBER, 0, '当前带宽');
  }

  protected executeImpl(): any {
    const optimizationType = this.getInputValue('optimizationType') as string;
    const targetLatency = this.getInputValue('targetLatency') as number;
    const maxBandwidth = this.getInputValue('maxBandwidth') as number;
    const compressionLevel = this.getInputValue('compressionLevel') as number;
    const enablePrediction = this.getInputValue('enablePrediction') as boolean;

    try {
      const world = this.context.getWorld();
      const networkSystem = world.getSystem('NetworkSystem');
      
      if (!networkSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '网络系统不可用' };
      }

      // 获取网络优化器
      const optimizer = networkSystem.getOptimizer();
      if (!optimizer) {
        this.setOutputValue('success', false);
        return { success: false, error: '网络优化器不可用' };
      }

      // 配置优化器
      const result = optimizer.configure({
        type: optimizationType,
        targetLatency,
        maxBandwidth,
        compressionLevel,
        enablePrediction
      });

      // 获取当前网络状态
      const networkStats = optimizer.getNetworkStats();

      this.setOutputValue('success', result.success);
      this.setOutputValue('optimizerId', result.optimizerId || '');
      this.setOutputValue('currentLatency', networkStats.latency || 0);
      this.setOutputValue('currentBandwidth', networkStats.bandwidth || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 带宽管理器节点
 */
export class BandwidthManagerNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'network/optimization/bandwidthManager',
      category: NodeCategory.NETWORK
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('maxUpload', SocketType.NUMBER, 1000, '最大上传(kbps)');
    this.addInputSocket('maxDownload', SocketType.NUMBER, 5000, '最大下载(kbps)');
    this.addInputSocket('priorityMode', SocketType.STRING, 'adaptive', '优先级模式');
    this.addInputSocket('qosEnabled', SocketType.BOOLEAN, true, '启用QoS');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('uploadUsage', SocketType.NUMBER, 0, '上传使用率');
    this.addOutputSocket('downloadUsage', SocketType.NUMBER, 0, '下载使用率');
    this.addOutputSocket('queueSize', SocketType.NUMBER, 0, '队列大小');
  }

  protected executeImpl(): any {
    const maxUpload = this.getInputValue('maxUpload') as number;
    const maxDownload = this.getInputValue('maxDownload') as number;
    const priorityMode = this.getInputValue('priorityMode') as string;
    const qosEnabled = this.getInputValue('qosEnabled') as boolean;

    try {
      const world = this.context.getWorld();
      const networkSystem = world.getSystem('NetworkSystem');
      
      if (!networkSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '网络系统不可用' };
      }

      // 获取带宽管理器
      const bandwidthManager = networkSystem.getBandwidthManager();
      if (!bandwidthManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '带宽管理器不可用' };
      }

      // 设置带宽限制
      const result = bandwidthManager.setLimits({
        maxUpload,
        maxDownload,
        priorityMode,
        qosEnabled
      });

      // 获取当前使用情况
      const usage = bandwidthManager.getUsage();

      this.setOutputValue('success', result.success);
      this.setOutputValue('uploadUsage', usage.uploadUsage || 0);
      this.setOutputValue('downloadUsage', usage.downloadUsage || 0);
      this.setOutputValue('queueSize', usage.queueSize || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册网络优化节点
 */
export function registerNetworkOptimizationNodes(registry: NodeRegistry): void {
  // P2P连接节点
  registry.registerNodeType({
    type: 'network/p2p/p2pConnection',
    category: NodeCategory.NETWORK,
    constructor: P2PConnectionNode,
    label: 'P2P连接',
    description: '建立点对点连接',
    tags: ['network', 'p2p', 'connection'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'network/p2p/p2pDataChannel',
    category: NodeCategory.NETWORK,
    constructor: P2PDataChannelNode,
    label: 'P2P数据通道',
    description: '创建和管理P2P数据通道',
    tags: ['network', 'p2p', 'datachannel'],
    version: '1.0.0'
  });

  // 网络优化节点
  registry.registerNodeType({
    type: 'network/optimization/networkOptimizer',
    category: NodeCategory.NETWORK,
    constructor: NetworkOptimizerNode,
    label: '网络优化器',
    description: '优化网络性能',
    tags: ['network', 'optimization'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'network/optimization/bandwidthManager',
    category: NodeCategory.NETWORK,
    constructor: BandwidthManagerNode,
    label: '带宽管理器',
    description: '管理网络带宽使用',
    tags: ['network', 'bandwidth'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'network/optimization/latencyCompensation',
    category: NodeCategory.NETWORK,
    constructor: LatencyCompensationNode,
    label: '延迟补偿',
    description: '补偿网络延迟',
    tags: ['network', 'latency', 'compensation'],
    version: '1.0.0'
  });
}

/**
 * 网络延迟补偿节点
 */
export class LatencyCompensationNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'network/optimization/latencyCompensation',
      category: NodeCategory.NETWORK
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('entity', SocketType.ENTITY, null, '目标实体');
    this.addInputSocket('compensationType', SocketType.STRING, 'interpolation', '补偿类型');
    this.addInputSocket('bufferSize', SocketType.NUMBER, 3, '缓冲区大小');
    this.addInputSocket('smoothingFactor', SocketType.NUMBER, 0.1, '平滑因子');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('compensatedPosition', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '补偿后位置');
    this.addOutputSocket('predictedPosition', SocketType.VECTOR3, { x: 0, y: 0, z: 0 }, '预测位置');
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const compensationType = this.getInputValue('compensationType') as string;
    const bufferSize = this.getInputValue('bufferSize') as number;
    const smoothingFactor = this.getInputValue('smoothingFactor') as number;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取网络组件
      const networkComponent = entity.getComponent('NetworkComponent');
      if (!networkComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有网络组件' };
      }

      // 获取延迟补偿系统
      const compensationSystem = networkComponent.getLatencyCompensation();
      if (!compensationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '延迟补偿系统不可用' };
      }

      // 配置延迟补偿
      const result = compensationSystem.configure({
        type: compensationType,
        bufferSize,
        smoothingFactor
      });

      // 获取补偿后的位置
      const compensatedPos = compensationSystem.getCompensatedPosition();
      const predictedPos = compensationSystem.getPredictedPosition();

      this.setOutputValue('success', result.success);
      this.setOutputValue('compensatedPosition', compensatedPos || { x: 0, y: 0, z: 0 });
      this.setOutputValue('predictedPosition', predictedPos || { x: 0, y: 0, z: 0 });
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

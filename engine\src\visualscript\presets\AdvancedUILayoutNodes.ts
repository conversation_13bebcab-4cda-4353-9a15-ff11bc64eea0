/**
 * AdvancedUILayoutNodes.ts
 * 
 * 高级UI布局节点 - 提供布局管理、主题系统、UI动画等高级UI功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

/**
 * 布局管理器节点
 */
export class LayoutManagerNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'ui/layout/layoutManager',
      category: NodeCategory.UI
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('container', SocketType.ENTITY, null, '容器实体');
    this.addInputSocket('layoutType', SocketType.STRING, 'flex', '布局类型');
    this.addInputSocket('direction', SocketType.STRING, 'row', '布局方向');
    this.addInputSocket('justifyContent', SocketType.STRING, 'flex-start', '主轴对齐');
    this.addInputSocket('alignItems', SocketType.STRING, 'stretch', '交叉轴对齐');
    this.addInputSocket('gap', SocketType.NUMBER, 0, '间距');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('layoutId', SocketType.STRING, '', '布局ID');
  }

  protected executeImpl(): any {
    const container = this.getInputValue('container') as Entity;
    const layoutType = this.getInputValue('layoutType') as string;
    const direction = this.getInputValue('direction') as string;
    const justifyContent = this.getInputValue('justifyContent') as string;
    const alignItems = this.getInputValue('alignItems') as string;
    const gap = this.getInputValue('gap') as number;

    if (!container) {
      this.setOutputValue('success', false);
      return { success: false, error: '容器实体不能为空' };
    }

    try {
      // 获取UI组件
      const uiComponent = container.getComponent('UIComponent');
      if (!uiComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '容器没有UI组件' };
      }

      // 获取布局系统
      const layoutSystem = uiComponent.getLayoutSystem();
      if (!layoutSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '布局系统不可用' };
      }

      // 设置布局
      const result = layoutSystem.setLayout({
        type: layoutType,
        direction,
        justifyContent,
        alignItems,
        gap
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('layoutId', result.layoutId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * Flex布局节点
 */
export class FlexLayoutNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'ui/layout/flexLayout',
      category: NodeCategory.UI
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('element', SocketType.ENTITY, null, 'UI元素');
    this.addInputSocket('flex', SocketType.NUMBER, 1, 'flex值');
    this.addInputSocket('flexGrow', SocketType.NUMBER, 0, 'flex-grow');
    this.addInputSocket('flexShrink', SocketType.NUMBER, 1, 'flex-shrink');
    this.addInputSocket('flexBasis', SocketType.STRING, 'auto', 'flex-basis');
    this.addInputSocket('alignSelf', SocketType.STRING, 'auto', '自身对齐');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
  }

  protected executeImpl(): any {
    const element = this.getInputValue('element') as Entity;
    const flex = this.getInputValue('flex') as number;
    const flexGrow = this.getInputValue('flexGrow') as number;
    const flexShrink = this.getInputValue('flexShrink') as number;
    const flexBasis = this.getInputValue('flexBasis') as string;
    const alignSelf = this.getInputValue('alignSelf') as string;

    if (!element) {
      this.setOutputValue('success', false);
      return { success: false, error: 'UI元素不能为空' };
    }

    try {
      // 获取UI组件
      const uiComponent = element.getComponent('UIComponent');
      if (!uiComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '元素没有UI组件' };
      }

      // 设置Flex属性
      const result = uiComponent.setFlexProperties({
        flex,
        flexGrow,
        flexShrink,
        flexBasis,
        alignSelf
      });

      this.setOutputValue('success', result.success);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 主题管理器节点
 */
export class ThemeManagerNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'ui/theme/themeManager',
      category: NodeCategory.UI
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('themeName', SocketType.STRING, 'default', '主题名称');
    this.addInputSocket('themeData', SocketType.OBJECT, {}, '主题数据');
    this.addInputSocket('applyGlobally', SocketType.BOOLEAN, true, '全局应用');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
    this.addOutputSocket('themeId', SocketType.STRING, '', '主题ID');
  }

  protected executeImpl(): any {
    const themeName = this.getInputValue('themeName') as string;
    const themeData = this.getInputValue('themeData') as any;
    const applyGlobally = this.getInputValue('applyGlobally') as boolean;

    if (!themeName) {
      this.setOutputValue('success', false);
      return { success: false, error: '主题名称不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const uiSystem = world.getSystem('UISystem');
      
      if (!uiSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'UI系统不可用' };
      }

      // 获取主题管理器
      const themeManager = uiSystem.getThemeManager();
      if (!themeManager) {
        this.setOutputValue('success', false);
        return { success: false, error: '主题管理器不可用' };
      }

      // 设置主题
      const result = themeManager.setTheme({
        name: themeName,
        data: themeData,
        applyGlobally
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('themeId', result.themeId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 样式表节点
 */
export class StyleSheetNode extends FunctionNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'ui/style/styleSheet',
      category: NodeCategory.UI
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('element', SocketType.ENTITY, null, 'UI元素');
    this.addInputSocket('className', SocketType.STRING, '', '样式类名');
    this.addInputSocket('styles', SocketType.OBJECT, {}, '样式对象');
    this.addInputSocket('important', SocketType.BOOLEAN, false, '重要性');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '设置成功');
  }

  protected executeImpl(): any {
    const element = this.getInputValue('element') as Entity;
    const className = this.getInputValue('className') as string;
    const styles = this.getInputValue('styles') as any;
    const important = this.getInputValue('important') as boolean;

    if (!element) {
      this.setOutputValue('success', false);
      return { success: false, error: 'UI元素不能为空' };
    }

    try {
      // 获取UI组件
      const uiComponent = element.getComponent('UIComponent');
      if (!uiComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '元素没有UI组件' };
      }

      // 应用样式
      let result;
      if (className) {
        result = uiComponent.addClass(className);
      } else if (styles) {
        result = uiComponent.setStyles(styles, important);
      } else {
        this.setOutputValue('success', false);
        return { success: false, error: '必须提供样式类名或样式对象' };
      }

      this.setOutputValue('success', result.success);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * UI过渡动画节点
 */
export class UITransitionNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'ui/animation/uiTransition',
      category: NodeCategory.UI
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInputSocket('element', SocketType.ENTITY, null, 'UI元素');
    this.addInputSocket('property', SocketType.STRING, 'opacity', '动画属性');
    this.addInputSocket('fromValue', SocketType.ANY, null, '起始值');
    this.addInputSocket('toValue', SocketType.ANY, null, '结束值');
    this.addInputSocket('duration', SocketType.NUMBER, 300, '持续时间(ms)');
    this.addInputSocket('easing', SocketType.STRING, 'ease', '缓动函数');
    
    // 输出插槽
    this.addOutputSocket('success', SocketType.BOOLEAN, false, '动画成功');
    this.addOutputSocket('animationId', SocketType.STRING, '', '动画ID');
  }

  protected async executeAsyncImpl(): Promise<any> {
    const element = this.getInputValue('element') as Entity;
    const property = this.getInputValue('property') as string;
    const fromValue = this.getInputValue('fromValue');
    const toValue = this.getInputValue('toValue');
    const duration = this.getInputValue('duration') as number;
    const easing = this.getInputValue('easing') as string;

    if (!element) {
      this.setOutputValue('success', false);
      return { success: false, error: 'UI元素不能为空' };
    }

    try {
      // 获取UI组件
      const uiComponent = element.getComponent('UIComponent');
      if (!uiComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '元素没有UI组件' };
      }

      // 获取动画系统
      const animationSystem = uiComponent.getAnimationSystem();
      if (!animationSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'UI动画系统不可用' };
      }

      // 执行过渡动画
      const result = await animationSystem.transition({
        property,
        fromValue,
        toValue,
        duration,
        easing
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('animationId', result.animationId || '');
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: error.message };
    }
  }
}

/**
 * 注册高级UI布局节点
 */
export function registerAdvancedUILayoutNodes(registry: NodeRegistry): void {
  // 布局管理节点
  registry.registerNodeType({
    type: 'ui/layout/layoutManager',
    category: NodeCategory.UI,
    constructor: LayoutManagerNode,
    label: '布局管理器',
    description: '管理容器的布局方式',
    tags: ['ui', 'layout', 'manager'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'ui/layout/flexLayout',
    category: NodeCategory.UI,
    constructor: FlexLayoutNode,
    label: 'Flex布局',
    description: '设置元素的Flex布局属性',
    tags: ['ui', 'layout', 'flex'],
    version: '1.0.0'
  });

  // 主题和样式节点
  registry.registerNodeType({
    type: 'ui/theme/themeManager',
    category: NodeCategory.UI,
    constructor: ThemeManagerNode,
    label: '主题管理器',
    description: '管理UI主题',
    tags: ['ui', 'theme'],
    version: '1.0.0'
  });

  registry.registerNodeType({
    type: 'ui/style/styleSheet',
    category: NodeCategory.UI,
    constructor: StyleSheetNode,
    label: '样式表',
    description: '应用CSS样式',
    tags: ['ui', 'style', 'css'],
    version: '1.0.0'
  });

  // UI动画节点
  registry.registerNodeType({
    type: 'ui/animation/uiTransition',
    category: NodeCategory.UI,
    constructor: UITransitionNode,
    label: 'UI过渡动画',
    description: '创建UI元素的过渡动画',
    tags: ['ui', 'animation', 'transition'],
    version: '1.0.0'
  });
}
